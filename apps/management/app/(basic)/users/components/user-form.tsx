"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"

import type { User, UserList, CreateUser } from "../page"
// 创建用户的表单验证模式（密码必填）
const createUserSchema = z.object({
  login_name: z.string().min(1, "账户名不能为空"),
  password: z.string().min(6, "密码至少需要6个字符"),
})

// 编辑用户的表单验证模式（密码可选）
const updateUserSchema = z.object({
  login_name: z.string().min(1, "账户名不能为空"),
  password: z.string().optional(),
})

// 定义表单值类型
type CreateFormValues = z.infer<typeof createUserSchema>
type UpdateFormValues = z.infer<typeof updateUserSchema>

interface UserFormProps {
  user?: Partial<UserList>
  onSave: (user: User) => void
  isCreating: boolean
  isLoading: boolean
}

export function UserForm({ user, onSave, isCreating,isLoading }: UserFormProps) {
  // 根据是否为创建模式选择不同的验证模式
  const schema = isCreating ? createUserSchema : updateUserSchema

  // 初始化表单
  const form = useForm<any>({
    resolver: zodResolver(schema),
    defaultValues: user
      ? { ...user, password: "" }
      : {
          login_name: "",
          password: "",
        },
    // 当模式切换时重新验证表单
    mode: "onChange"
  })

  // 保存用户
  const handleSubmit = isCreating
    ? (values: CreateFormValues) => {
        // 创建用户 - 不需要 user_id，但密码必填
        onSave({
          ...values,
        } as CreateUser)
      }
    : (values: UpdateFormValues) => {
        // 编辑用户 - 需要 user_id，密码可选
        // 如果密码为空，则不包含在提交的数据中
        const updateData: any = {
          login_name: values.login_name,
        }

        // 只有当密码不为空时，才包含密码字段
        if (values.password && values.password.trim() !== '') {
          updateData.password = values.password
        }

        onSave(updateData as User)
      }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="login_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>账户名</FormLabel>
              <FormControl>
                <Input placeholder="输入账户名" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {isCreating ? "密码" : "修改密码"}
                {isCreating && <span className="text-destructive ml-1">*</span>}
              </FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={isCreating ? "输入密码" : "留空表示不修改密码"}
                  {...field}
                  required={isCreating}
                />
              </FormControl>
              <FormMessage />
              {!isCreating && (
                <p className="text-xs text-muted-foreground">
                  如果不需要修改密码，请留空
                </p>
              )}
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isLoading}>
           {isLoading && <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />}
            {isCreating ? "创建账户" : "保存修改"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
