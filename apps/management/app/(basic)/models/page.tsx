"use client"

import { use<PERSON><PERSON>back, useState } from "react"
import { ChevronDown, ChevronRight, Trash2 } from "lucide-react"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import { Card } from "@ragtop-web/ui/components/card"
import Image from "next/image"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useSubmitApiKey } from "./hooks"
import { IconMap } from "@/common/model"
import { isLocalLlmFactory } from "./utils"
import { ApiKeyModal } from "./components/api-key-modal"
import { ConfiguredModelList, Model, useDeleteModel, useExistingModels, useUnconfiguredModels } from "@/service/model-service"
import { AlertDialog, AlertDialogContent, AlertDialogTitle, AlertDialogDescription, AlertDialogCancel, AlertDialogAction } from "@radix-ui/react-alert-dialog"
import { <PERSON><PERSON><PERSON><PERSON>ogHeader, <PERSON>ertD<PERSON>ogFooter } from "@ragtop-web/ui/components/alert-dialog"


// 待配置模型卡片组件
function ModelCard({ model, onConfigure }: { model: Model; onConfigure?: () => void }) {
  const icon = IconMap[model.name as keyof typeof IconMap];


  return (
    <Card
      className="relative flex flex-col p-4 hover:border-primary/50 transition-colors"
    >
      <div className="flex items-center mb-2">
        <div className="w-12 h-12 flex items-center justify-center bg-muted rounded-md mr-3">
          {/* 使用图片或SVG图标 */}
          <Image src={icon} alt={model.name} width={32} height={32} />
        </div>
        <div>
          <h3 className="font-semibold">{model.name}</h3>
          <p className="text-xs text-muted-foreground">{model.tags}</p>
        </div>
      </div>

      <div className="mt-auto">
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2"
          onClick={onConfigure}
        >
          添加模型
        </Button>
      </div>
    </Card>
  )
}

// 已配置模型卡片组件
function ConfigureModelCard({ model, onDelete }: { model: ConfiguredModelList, onDelete: () => void }) {
  const icon = IconMap[model.llm_factory as keyof typeof IconMap];

    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

      const handleOpenDeleteDialog = () => {
        setIsDeleteDialogOpen(true)
      }

  // // 判断按钮文本
  // const getButtonText = (modelId: string) => {
  //   // 根据需求中的逻辑判断
  //   if (
  //     isLocalLlmFactory(modelId) ||
  //     // modelId === "volc-engine" ||
  //     // modelId === "tencent-hunyuan" ||
  //     // modelId === "xunfei-spark" ||
  //     // modelId === "baidu-yiyan" ||
  //     // modelId === "fish-audio" ||
  //     // modelId === "tencent-cloud" ||
  //     // modelId === "google-cloud" ||
  //     modelId === "azure-openai"
  //   ) {
  //     return "添加模型";
  //   }
  //   return "API-Key";
  // };


  return (
    <Card className="relative p-4 hover:border-primary/50 transition-colors">
      <div className="flex justify-between items-start">
        <div className="flex items-center mb-2">
          <div className="w-12 h-12 flex items-center justify-center bg-muted rounded-md mr-3">
            <Image src={icon} alt={model.llm_factory} width={32} height={32} />
          </div>
          <div>
            <h3 className="font-semibold">{model.llm_factory}</h3>
            <p className="text-xs text-muted-foreground">{model.llm_name} {model.model_type}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" title="删除模型" onClick={handleOpenDeleteDialog}>
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">删除</span>
          </Button>
        </div>
      </div>
<AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除模型 "{model?.llm_factory}-{model?.llm_name}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={onDelete}>
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}

export default function ModelsPage() {
  const [configuredExpanded, setConfiguredExpanded] = useState(true)
  const [unconfiguredExpanded, setUnconfiguredExpanded] = useState(true)

  const { data: configuredModels } = useExistingModels()
  const { data: unconfiguredModels } = useUnconfiguredModels()
  const deleteMutate = useDeleteModel()

  const {
    saveApiKeyLoading,
    initialApiKey,
    llmFactory,
    onApiKeySavingOk,
    apiKeyVisible,
    hideApiKeyModal,
    showApiKeyModal,
  } = useSubmitApiKey();

  // const {
  //  NL2SQLSettinLoading,
  //   onNL2SQLSettingOk,
  //   NL2SQLSettingVisible,
  //   hideNL2SQLSettingModal,
  //   showNL2SQLSettingModal,
  // } = useSubmitNL2SQL()



  const toggleConfigured = () => {
    setConfiguredExpanded(!configuredExpanded)
  }

  const toggleUnconfigured = () => {
    setUnconfiguredExpanded(!unconfiguredExpanded)
  }

  const handleConfigureModel = useCallback(
    (model: Model | ConfiguredModelList) => {
      // 将 ConfiguredModelList 转换为 Model 类型
      if ('llm_id' in model) {
        // 这是一个已配置的模型
        const convertedModel: Model = {
          id: model.llm_id || '',
          name: model.llm_name || '',
          logo: '',
          tags: model.model_type?.toString() || '',
        };
        showApiKeyModal(convertedModel);
      } else {
        // 这是一个未配置的模型
        showApiKeyModal(model as Model);
      }
    },
    [showApiKeyModal],
  );

  const handleDelete= (model: ConfiguredModelList) => {
    deleteMutate.mutate({llm_id: model.llm_id})
  }

  return (
    <CustomContainer title="模型配置"
    // action={
    //   <Button onClick={showNL2SQLSettingModal}>
    //       <Settings className="h-4 w-4" />
    //       nl2sql配置
    //     </Button>
    // }
    >

      {/* 已配置的模型 */}
      <div className="mb-8">
        <button
          className="flex items-center text-l font-semibold mb-4"
          onClick={toggleConfigured}
        >
          {configuredExpanded ? <ChevronDown className="mr-2" /> : <ChevronRight className="mr-2" />}
          已配置的模型
        </button>

        {configuredExpanded && (
          <div className="grid grid-cols-1 gap-4">
            {configuredModels?.map((model) => (
              <ConfigureModelCard
                key={model.llm_id}
                model={model}
                onDelete={() => handleDelete(model)}
              />
            ))}
          </div>
        )}
      </div>

      {/* 待配置的模型 */}
      <div>
        <button
          className="flex items-center text-l font-semibold mb-4"
          onClick={toggleUnconfigured}
        >
          {unconfiguredExpanded ? <ChevronDown className="mr-2" /> : <ChevronRight className="mr-2" />}
          待配置的模型
        </button>

        {unconfiguredExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {unconfiguredModels?.map((model) => (
              <ModelCard
                key={model.name}
                model={model}
                onConfigure={() => handleConfigureModel(model)}
              />
            ))}
          </div>
        )}
      </div>

      {/* API Key 模态框 */}
    { apiKeyVisible && <ApiKeyModal
        open={apiKeyVisible}
        onClose={hideApiKeyModal}
        onSubmit={onApiKeySavingOk}
        loading={saveApiKeyLoading}
        initialApiKey={initialApiKey}
        llmFactory={llmFactory}
        title="添加 LLM"
      />}

      {/* <Nl2SQLDialog
      open={NL2SQLSettingVisible}
        onClose={hideNL2SQLSettingModal}
        onSubmit={onNL2SQLSettingOk}
        loading={NL2SQLSettinLoading}
        /> */}

    </CustomContainer>
  )
}
