"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import {
  ChevronsUpDown,
  LogOut,
  KeyRound,
} from "lucide-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@ragtop-web/ui/components/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@ragtop-web/ui/components/sidebar"
import { PasswordChangeDialog } from "@/components/password-change-dialog"
import { useLogout } from "@/service"

export function NavUser({
  user,
}: {
  user: {
    name: string
    email: string
    avatar: string
  }
}) {
  const router = useRouter()
  const { isMobile } = useSidebar()
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false)
  const logoutMutation = useLogout()

  // 处理打开密码修改对话框
  const handleOpenPasswordDialog = () => {
    setIsPasswordDialogOpen(true)
  }

  // 处理关闭密码修改对话框
  const handleClosePasswordDialog = () => {
    setIsPasswordDialogOpen(false)
  }

  // 处理退出登录
  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        // 清除登录状态
        localStorage.removeItem("isLoggedIn")
        localStorage.removeItem("user")

        // 跳转到登录页面
        router.push("/login")
      },
    })
  }

  return (
    <>
      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{user.name}</span>
                </div>
                <ChevronsUpDown className="ml-auto size-4" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
              side={isMobile ? "bottom" : "right"}
              align="end"
              sideOffset={4}
            >
              <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">{user.name}</span>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={handleOpenPasswordDialog}>
                  <KeyRound className="mr-2 h-4 w-4" />
                  账户管理
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                退出登录
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>

      {/* 密码修改对话框 */}
      <PasswordChangeDialog
        open={isPasswordDialogOpen}
        onClose={handleClosePasswordDialog}
      />
    </>
  )
}
