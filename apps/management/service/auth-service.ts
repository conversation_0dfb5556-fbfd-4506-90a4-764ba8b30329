/**
 * 认证服务
 * 
 * 提供登录、登出和token管理功能
 */

import { useMutation } from '@tanstack/react-query'
import { createApiClient, setAccessToken, clearAccessToken } from '@ragtop-web/ui/lib/api'
import { API_PREFIX } from './index'


// 创建API客户端，设置API前缀
const apiClientWithoutAuth = createApiClient({
  apiPrefix: `${API_PREFIX}/user`,
  requireAuth: false // 登录接口不需要认证
})

const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/user`,
})

// 登录响应接口
export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: {
    id: string
    name: string
    role: string
  }
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 修改密码接口
export interface PasswordModifyRequest {
  new_password: string
}


/**
 * 管理员登录
 */
export const useLogin = () => {
  return useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      const response = await apiClientWithoutAuth.post<LoginResponse>('/signin', credentials)
      
      // 保存token到localStorage
      if (response.access_token) {
        setAccessToken(response.access_token)
      }
      
      return response
    }
  })
}

/**
 * 管理员登出
 */
export const useLogout = () => {
  return useMutation({
    mutationFn: async () => {
      // 调用登出API
      try {
        await apiClient.post('/signout')
      } catch (error) {
        console.error('登出API调用失败', error)
      }
      
      // 无论API是否成功，都清除本地token
      clearAccessToken()
      
      return true
    }
  })
}

/**
 * 检查用户是否已登录
 */
export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') return false
  return !!localStorage.getItem('access_token')
}

/**
 * 修改密码
 */
export const  usePasswordModify=()=>{
  return useMutation({
    mutationFn: async (password:PasswordModifyRequest) => {
      const response = await apiClient.post('/change-password',password)
      return response
    }
  })
}