/**
 * 数据集服务
 *
 * 提供数据集相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createApiClient, PaginatedResponse } from '@ragtop-web/ui/lib/api'
import { API_PREFIX } from './index'

// 创建API客户端
const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/tableset`,
  requireTeamId:true
})

// 数据集接口
export interface Dataset {
  id: string
  tablesetName: string
  tablesetDescription?: string
  databaseName: string
  username: string
  password: string
  buildVersion: string | null
  buildingVersion: string | null
  createdAt: string
}

// 数据集创建/更新参数
export interface DatasetParams {
  id?: string
  tablesetName: string
  tablesetDescription?: string
  databaseName: string
  username: string
  password: string
}

// 表结构接口
export interface TableSchema {
  id: string
  name: string
  tables: TableItem[]
}

export interface TableItem {
  id: string
  name: string
  schemaId: string
}

// 文档接口
export interface Document {
  id: string
  name: string
  type: string
  description: string | null
  columnName: string
  columnType: string
  comment: string
  tableId: string
}

/**
 * 获取数据集列表
 * 分页查询
 */
export const useDatasets = (pageNumber = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ['datasets', pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<Dataset>>('/query', {}, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  })
}

/**
 * 获取单个数据集
 */
export const useDataset = () => {
  return useMutation({
    mutationFn: (data: { id: string }) => apiClient.post<Dataset>(`/describe`, data),
  })
}

/**
 * 创建数据集
 */
export const useCreateDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: DatasetParams) => apiClient.post<Dataset>(`/create`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
    },
  })
}

/**
 * 更新数据集
 */
export const useUpdateDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: DatasetParams & { id: string }) => apiClient.post<Dataset>(`/update`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
    },
  })
}

/**
 * 删除数据集
 */
export const useDeleteDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { id: string }) => apiClient.post(`/delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
    },
  })
}

/**
 * 构建数据集
 */
export const useBuildDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { id: string }) => apiClient.post(`/build`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
    },
  })
}

/**
 * 获取数据集表结构
 */
export const useDatasetSchemas = () => {
  return useMutation({
    mutationFn: (data: { id: string }) => apiClient.post<TableSchema[]>(`/schemas`, data),
  })
}

/**
 * 获取数据集表文档
 */
export const useDatasetDocuments = () => {
  return useMutation({
    mutationFn: (data: { id: string, tableId?: string }) => apiClient.post<Document[]>(`/documents`, data),
  })
}

/**
 * 更新文档注释
 */
export const useUpdateDocument = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { id: string, description: string }) => apiClient.post(`/update-document`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasetDocuments'] })
    },
  })
}

/**
 * 测试数据集连接
 */
export const useTestConnection = () => {
  return useMutation({
    mutationFn: (data: Omit<DatasetParams, 'id'>) => apiClient.post<{ success: boolean, message: string }>(`/test-connection`, data),
  })
}
