/**
 * 文件服务
 *
 * 提供文件相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createWebApiClient, PaginatedResponse } from "@/lib/api/web-fetch-client";
import { API_PREFIX } from "./index";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${API_PREFIX}/file`,
  requireTeamId:true
});

// 文件接口
export interface FileItem {
  id: string;
  name: string;
  type: string;
  size: number; // 文件大小（字节）
  references: number; // 被链接的知识库数量
  createdAt: string; // 创建时间
  isFolder: boolean; // 是否是文件夹
  parentId: string | null; // 父文件夹ID，null表示根目录
  path: string; // 完整路径
}

// 知识库引用接口
export interface KnowledgeBaseReference {
  id: string;
  name: string;
}

/**
 * 获取文件列表
 * 分页查询
 */
export const useFiles = (pageNumber = 1, pageSize = 20) => {
  return useQuery({
    queryKey: ["files", pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<FileItem>>("/query", {}, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  });
};

/**
 * 获取文件夹内容
 */
export const useFolderContents = () => {
  return useMutation({
    mutationFn: (data: { parentId: string | null }) =>
      apiClient.post<FileItem[]>("/folder-contents", data),
  });
};

/**
 * 上传文件
 */
export const useUploadFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FormData) =>
      apiClient.post<FileItem>("/upload", data, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 创建文件夹
 */
export const useCreateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { name: string; parentId: string | null }) =>
      apiClient.post<FileItem>("/create-folder", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 删除文件
 */
export const useDeleteFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { file_id: string }) =>
      apiClient.post("/delete", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 重命名文件
 */
export const useRenameFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { file_id: string; name: string }) =>
      apiClient.post<FileItem>("/rename", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 移动文件
 */
export const useMoveFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { file_id: string; parentId: string | null }) =>
      apiClient.post<FileItem>("/move", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 获取文件的知识库引用
 */
export const useFileReferences = () => {
  return useMutation({
    mutationFn: (data: { file_id: string }) =>
      apiClient.post<KnowledgeBaseReference[]>("/references", data),
  });
};
