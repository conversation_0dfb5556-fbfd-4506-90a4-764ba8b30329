/**
 * Agent服务
 *
 * 提供Agent相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createApiClient, PaginatedResponse } from "@ragtop-web/ui/lib/api";
import { API_PREFIX } from "./index";

// 创建API客户端
const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/agent`,
  requireTeamId:true
});

export interface AgentSettings {
  agent_prompt: string;
  keyword_weight: number;
  llm_model: string;
  similarity_threshold: number;
  temperature: number;
  top_n: number;
  top_p: number;
}

export interface Agents {
  create_time: number;
  id: string;
  owner_id: string;
  resource_ids: string[];
  settings: AgentSettings;
  title: string;
  type: string;
  visibility: string;
}


/**
 * CreateAgentRequest
 */
export interface AgentCreateParams {
    abnormal_message?: string;
    description?: string;
    hello_message?: string;
    link_resources?: string[];
    name?: string;
    prompt?: string;
    scope?: Scope;
    settings?: TeamAgentSettings;
    team_id?: string;
    [property: string]: any;
}

/**
 * 使用范围
 */
export enum Scope {
    Private = "PRIVATE",
    TeamPublic = "TEAM_PUBLIC",
}

/**
 * TeamAgentSettings
 */
export interface TeamAgentSettings {
    /**
     * 自由度
     */
    flexibility?: Flexibility;
    keyword_weight?: number;
    llm_model?: string;
    similarity_threshold?: number;
    temperature?: number;
    top_n?: number;
    top_p?: number;
    [property: string]: any;
}

/**
 * 自由度
 */
export enum Flexibility {
    Balanced = "BALANCED",
    Deliberate = "DELIBERATE",
    Improvised = "IMPROVISED",
}

/**
 * 获取Agent列表
 * 分页查询
 */
export const useAgents = (pageNumber = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ["agents", pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<Agents>>("/query", {}, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  });
};

/**
 * 获取单个Agent
 */
export const useAgent = () => {
  return useMutation({
      mutationFn: (data: { agent_id: string }) =>
        apiClient.post<Agents>(`describe`, data),
    });
};

/**
 * 创建Agent
 */
export const useCreateAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AgentCreateParams) =>
      apiClient.post<Agents>("create", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
  });
};

/**
 * 更新Agent
 */
export const useUpdateAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data : Partial<AgentCreateParams> & { id: string }) =>
      apiClient.post<Agents>(`modify`, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
      queryClient.invalidateQueries({ queryKey: ["agents", data.id] });
    },
  });
};

/**
 * 删除Agent
 */
export const useDeleteAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { agent_id: string }) => apiClient.post(`delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
  });
};

/**
 * 获取Agent会话列表
 * 分页查询
 */
export const useAgentSessions = (agentId: string, pageNumber = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ["agents", agentId, "sessions", pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<any>>(`sessions/query`, { agent_id: agentId }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled: !!agentId,
  });
};

/**
 * 获取会话消息列表
 * 分页查询
 */
export const useSessionMessages = (sessionId: string, pageNumber = 1, pageSize = 20) => {
  return useQuery({
    queryKey: ["sessions", sessionId, "messages", pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<any>>(`messages/query`, { session_id: sessionId }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled: !!sessionId,
  });
};

/**
 * 发送消息
 */
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      sessionId,
      content,
    }: {
      sessionId: string;
      content: string;
    }) =>
      apiClient.post<any>(`messages/create`, { session_id: sessionId, content }),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["sessions", variables.sessionId, "messages"],
      });
    },
  });
};
