/**
 * 团队服务
 *
 * 提供团队相关的API请求方法和团队ID管理
 */

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createWebApiClient } from "@/lib/api/web-fetch-client";
import { API_PREFIX } from "./index";


// 成员API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${API_PREFIX}/team`,
  requireTeamId:true
});

export interface Team {
  id: string;
  roles: string[];
  title: string;
}

export interface User {
  create_time: number;
  id: string;
  nick: string;
  teams: Team[];
  username: string;
}

// 团队成员详情接口
export interface UserList {
  create_time: number;
  id: string;
  roles?: string[];
  user: User;
}

/**
 * 获取当前用户信息
 */
export const useLLMSettings = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data: any) =>
      apiClient.post(`/modify-llm-settings`, data),
     onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  });
};


