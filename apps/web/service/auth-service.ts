/**
 * 认证服务
 *
 * 提供登录、登出和token管理功能
 */

import { useMutation} from '@tanstack/react-query'
import { createApiClient, setAccessToken, clearAccessToken } from '@ragtop-web/ui/lib/api'
import { API_PREFIX } from './index'
import { useSetAtom } from 'jotai'
import { setCurrentTeamAtom } from '@/store/team-store'

// 创建API客户端，设置API前缀
const apiClientWithoutAuth = createApiClient({
  apiPrefix: `${API_PREFIX}/user`,
  requireAuth: false // 登录接口不需要认证
})

const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/user`,
})

// 登录响应接口
export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: {
    id: string
    name: string
    role: string
  }
  teams?: {
    id: string
    title: string
    roles?: string[]
  }[]
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 修改密码接口
export interface PasswordModifyRequest {
  new_password: string
}

/**
 * 用户登录
 */
export const useLogin = () => {
  return useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      const response = await apiClientWithoutAuth.post<LoginResponse>('/signin', credentials)

      // 保存token到localStorage
      if (response.access_token) {
        setAccessToken(response.access_token)
      }

      return response
    }
  })
}

/**
 * 用户登出
 */
export const useLogout = () => {
  // 获取Jotai的设置函数
  const setCurrentTeam = useSetAtom(setCurrentTeamAtom)

  return useMutation({
    mutationFn: async () => {
      // 调用登出API
      try {
        await apiClient.post('/signout')
      } catch (error) {
        console.error('登出API调用失败', error)
      }

      // 无论API是否成功，都清除所有本地存储的数据
      clearAccessToken()

      // 清除团队ID
      if (typeof window !== 'undefined') {
        localStorage.removeItem('team_id')
      }

      // 清除用户信息
      if (typeof window !== 'undefined') {
        localStorage.removeItem('isLoggedIn')
        localStorage.removeItem('user')

        // 清除其他可能存在的缓存数据
        localStorage.removeItem('currentTeam')

        // 清除所有以 ragtop- 开头的本地存储项（如果有的话）
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('ragtop-')) {
            localStorage.removeItem(key)
          }
        })
      }

      // 清除Jotai状态
      setCurrentTeam(null)

      return true
    }
  })
}

/**
 * 检查用户是否已登录
 */
export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') return false
  return !!localStorage.getItem('access_token')
}


/**
 * 获取可添加的团队成员列表（分页查询）
 *
 * @param params - 查询参数，包括页码、每页数量和关键词
 */
export const useAvailableMembers = () => {
  return useMutation({
    mutationFn: async (params: {
      page_number?: number;
      page_size?: number;
      keyword?: string;
    }) => {
      const response = await apiClient.post('/query-memberships', params, {
        isPaginated: true,
        requireTeamId: true
      })
      return response
    }
  })
}

// /**
//  * 获取当前账号所在的团队列表
//  */
// export const useCurrentTeams = () => {
//   return useMutation({
//     mutationFn: async () => {
//       const response = await apiClient.post('/query-memberships', {}, {
//         isPaginated: false,
//         requireTeamId: true
//       })
//       return response
//     }
//   })
// }

/**
 * 修改密码
 */
export const  usePasswordModify=()=>{
  return useMutation({
    mutationFn: async (password:PasswordModifyRequest) => {
      const response = await apiClient.post('/current/modify-password',password)
      return response
    }
  })
}

