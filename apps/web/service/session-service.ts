/**
 * session服务
 *
 * 提供session相关的API请求方法
 */

import {  useMutation, useQueryClient } from "@tanstack/react-query";
import { createWebApiClient } from "@/lib/api/web-fetch-client";
import { API_PREFIX } from "./index";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${API_PREFIX}/agent-session`,
  requireTeamId:true
});

export interface SessionCreateParams {
  agent_id:string
  title: string
}

export interface Sessions {
  id: string;
  title: string;
}

export interface SessionHistory {
  id: string
}

export interface Message {
  agent_id: string;
  session_id: string;
  text_content: string;
}

/**
 * 获取session列表
 * TODO: 分页查询
 */
export const useSessions = () => {
  return useMutation({
      mutationFn: (data: "agent_id" | "keyword") =>
        apiClient.post<Sessions[]>(`query`, data),
    });
};

/**
 * 获取session历史
 * TODO：分页查询
 */
export const useAgent = () => {
  return useMutation({
    mutationFn: (data: "session_id" | "keyword") => apiClient.post<SessionHistory[]>(`describe-history`, data),
  });
};

/**
 * 创建session
 */
export const useCreateSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SessionCreateParams) =>
      apiClient.post<Sessions>("create", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sessions"] });
    },
  });
};

/**
 * 更新Agent
 */
export const useUpdateAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<SessionCreateParams> & { session_id: string }) =>
      apiClient.post<Sessions>(`modify`, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["sessions"] });
      queryClient.invalidateQueries({ queryKey: ["sessions", data.id] });
    },
  });
};

/**
 * 删除Agent
 */
export const useDeleteAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: "session_id") => apiClient.post(`delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sessions"] });
    },
  });
};


/**
 * 发送消息
 */
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data:Message) =>
      apiClient.post(`chat-completion`, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["sessions", variables.session_id, "messages"],
      });
    },
  });
};
