/**
 * 知识库服务
 *
 * 提供知识库相关的API请求方法
 */

import {  useMutation } from "@tanstack/react-query";
import { createApiClient } from "@ragtop-web/ui/lib/api";
import { API_PREFIX } from "./index";

// 创建API客户端
const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/kbase-doc`,
  requireTeamId:true
});

// API基础路径

// 知识库接口
export interface KnowledgeBaseDoc {
  kbase_id: string;
  keyword?: string[];
  document_id: string;
}

export interface ChunkingConfig {
  chunking_provider_id: string;
  pdf_pages: [
    {
      end: number;
      start: number;
    }
  ];
  pdf_parser_config: {
    parser_provider_id: string;
  };
}

// 知识库详情接口
export interface KnowledgeBaseDocList {
  chunk_num: number;
  chunking_config: ChunkingConfig;
  create_time: number;
  enabled: true;
  infra_res_id: string;
  name: string;
  process_begin_time: number;
  process_duration: number;
  progress: number;
  progress_msg: string;
  size: number;
  status: string;
  type: string;
}

/**
 * 获取知识库详情列表
 * TODO: 分页查询
 */
export const useKnowledgeBaseDoc = () => {
  return useMutation({
    mutationFn: (data: Omit<KnowledgeBaseDoc, "kbase_id" | "keyword">) =>
      apiClient.post<KnowledgeBaseDocList[]>("/query", data),
  });
};

/**
 * 启用文档
 */
export const useKBaseDocEnable = () => {
  return useMutation({
    mutationFn: (data: Omit<KnowledgeBaseDoc, "document_id">) =>
      apiClient.post(`enable`, data),
  });
};

/**
 * 禁用文档
 * @returns 
 */
export const useKBaseDocDisabled=()=>{
  return useMutation({
    mutationFn: (data: Omit<KnowledgeBaseDoc, "document_id">) =>
      apiClient.post(`disable`, data),
  });
}

/**
 * 开始解析
 * @returns 
 */

export const useKBaseDocStartParse=()=>{
  return useMutation({
    mutationFn: (data: Omit<KnowledgeBaseDoc, "document_id">) =>
      apiClient.post(`start-parse`, data),
  })
}

/**
 * 终止解析
 * @returns 
 */
export const useKBaseDocStopParse=()=>{
  return useMutation({
    mutationFn: (data: Omit<KnowledgeBaseDoc, "document_id">) =>
      apiClient.post(`stop-parse`, data),
  })
}

/**
 * 将文档从知识库中移除
 * @returns 
 */
export const useKBaseDocDelete=()=>{
  return useMutation({
    mutationFn: (data: Omit<KnowledgeBaseDoc, "document_id">) =>
      apiClient.post(`delete`, data),
  })
}

/**
 * 修改切片方法
 */

export const useKBaseDocModifyChunking=()=>{
  return useMutation({
    mutationFn: (data: ChunkingConfig) =>
      apiClient.post(`modify-chunking-config`, data),
  })
}
