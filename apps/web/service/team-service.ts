/**
 * 团队服务
 *
 * 提供团队相关的API请求方法和团队ID管理
 */

import { useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createWebApiClient, setTeamId, getTeamId } from "@/lib/api/web-fetch-client";
import { API_PREFIX } from "./index";
import { useAtom, useSetAtom } from "jotai";
import { teamIdAtom, setCurrentTeamAtom, initTeamFromUserAtom } from "@/store/team-store";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: API_PREFIX,
  requireTeamId: false // 获取团队列表时不需要团队ID
});

// 成员API客户端
const memberApiClient = createWebApiClient({
  apiPrefix: `${API_PREFIX}/member`,
  requireTeamId:true
});

export interface Team {
  id: string;
  roles: string[];
  title: string;
}

export interface User {
  create_time: number;
  id: string;
  nick: string;
  teams: Team[];
  username: string;
}

// 团队成员详情接口
export interface UserList {
  create_time: number;
  id: string;
  roles?: string[];
  user: User;
}

/**
 * 获取当前用户信息
 */
export const useCurrentUser = () => {
  return useQuery<User>({
    queryKey: ['currentUser'],
    queryFn: async () => {
      const user = await apiClient.post<User>('/user/current',{});

      // 如果用户有团队，并且本地没有存储团队ID，则存储第一个团队的ID
      if (user.teams && user.teams.length > 0) {
        const storedTeamId = getTeamId();
        if (!storedTeamId && user.teams[0]?.id) {
          setTeamId(user.teams[0].id);
        }
      }

      return user;
    }
  });
};

/**
 * 切换当前团队
 */
export const useSwitchTeam = () => {
  const queryClient = useQueryClient();
  const setTeam = useSetAtom(setCurrentTeamAtom);

  return useMutation({
    mutationFn: (team: Team) => {
      // 使用Jotai设置当前团队
      setTeam(team);
      // 保持兼容性，同时更新localStorage
      setTeamId(team.id);
      return Promise.resolve(team);
    },
    onSuccess: () => {
      // 切换团队后，刷新所有可能受影响的查询
      queryClient.invalidateQueries();
    }
  });
};

/**
 * 获取当前选中的团队ID
 */
export const useCurrentTeamId = () => {
  // 使用Jotai获取当前团队ID
  const [teamId] = useAtom(teamIdAtom);
  return teamId;
};

/**
 * 获取团队列表
 */
export const useTeams = () => {
  const { data: currentUser, isLoading } = useCurrentUser();
  const [teamId] = useAtom(teamIdAtom);
  const setTeams = useSetAtom(initTeamFromUserAtom);

  // 当用户数据加载完成后，初始化团队信息
  useEffect(() => {
    if (currentUser?.teams && currentUser.teams.length > 0) {
      setTeams(currentUser.teams);
    }
  }, [currentUser, setTeams]);

  return {
    data: currentUser?.teams || [],
    isLoading,
    currentTeamId: teamId
  };
};

/**
 * 获取团队成员详情列表
 * 分页查询
 */

/**
 * 获取用户列表（分页）
 *
 * @param pageNumber - 当前页码
 * @param pageSize - 每页条数
 * @param keyword - 搜索关键词
 */
export const useMembers = (pageNumber: number = 1, pageSize: number = 10, keyword?: string) => {
  return useQuery({
    queryKey: ['teams', pageNumber, pageSize, keyword],
    queryFn: () => memberApiClient.post<{
      records: UserList[],
      total: number,
      page_number: number,
      page_size: number
    }>('/query', {
      keyword
    }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  })
}

/**
 * 添加成员
 */
export const useMemberCreate = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data: any) =>
      memberApiClient.post(`/create`, data),
     onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  });
};

/**
 * 删除成员
 */
export const useMemberDelete = () => {
    const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data: any) =>
      memberApiClient.post(`/delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  });
};

/**
 * 查询某个成员详细信息
 */
export const useMemberDescribe = () => {
    const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) =>
      memberApiClient.post<User>(`current`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  });
};
