/**
 * 团队状态管理
 *
 * 使用Jotai管理团队相关的状态
 */

import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import type { Team } from '@/service/team-service'

// 定义登录响应中的团队类型
export interface LoginTeam {
  id: string
  title: string
  roles?: string[]
}

/**
 * 当前团队ID的atom
 * 使用localStorage持久化存储
 */
export const teamIdAtom = atomWithStorage<string | null>('team_id', null)

/**
 * 当前团队信息的atom
 */
export const currentTeamAtom = atom<Team | null>(null)

/**
 * 当前用户是否为团队管理员的atom
 */
export const isTeamAdminAtom = atom<boolean>(false)

/**
 * 设置当前团队信息
 *
 * 同时更新团队ID和团队管理员状态
 */
export const setCurrentTeamAtom = atom(
  null,
  (get, set, team: Team | LoginTeam | null) => {
    // 更新团队信息
    set(currentTeamAtom, team as Team | null)

    // 更新团队ID
    set(teamIdAtom, team?.id || null)

    // 更新管理员状态
    const isAdmin = team?.roles?.includes('TEAM_ADMIN') || false
    set(isTeamAdminAtom, isAdmin)
  }
)

/**
 * 获取当前团队信息
 *
 * 从用户的团队列表中查找当前选中的团队
 */
export const initTeamFromUserAtom = atom(
  null,
  (get, set, teams: (Team | LoginTeam)[]) => {
    const teamId = get(teamIdAtom)

    if (teams && teams.length > 0) {
      // 查找当前团队
      const currentTeam = teamId
        ? teams.find(team => team.id === teamId)
        : teams[0]

      if (currentTeam) {
        // 设置当前团队信息
        set(setCurrentTeamAtom, currentTeam)
      } else if (teams[0]) {
        // 如果没有找到当前团队，使用第一个团队
        set(setCurrentTeamAtom, teams[0])
      }
    } else {
      // 如果没有团队，清空当前团队信息
      set(setCurrentTeamAtom, null)
    }
  }
)
