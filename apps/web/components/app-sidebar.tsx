"use client"

import * as React from "react"
import { useState } from "react"
import {
  UserRound,
  Bot,
  Database,
  BookOpenText,
  FileText,
  Plus,
} from "lucide-react"

import { NavMain } from "@/components/nav/nav-main"
import { NavProjects } from "@/components/nav/nav-projects"
import { NavUser } from "@/components/nav/nav-user"
import { TeamSwitcher } from "@/components/nav/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@ragtop-web/ui/components/sidebar"
import { NavSecondary } from "./nav/nav-secondary"
import { Button } from "@ragtop-web/ui/components/button"
import { AgentDialog } from "@/components/agent/agent-dialog"
import { useIsTeamAdmin, useCurrentUser } from "@/lib/user-role"

// 模拟数据
const mockData = {
  navMainTeam: [
    {
      title: "Agent 1",
      url: "/agent/agentaaa",
      isActive: true,
      items: [
        {
          title: "History",
          url: "/agent/agentaaa/sessionaaa",
        },
        {
          title: "Starred",
          url: "/agent/agentaaa/sessionbbb",
        },
        {
          title: "Settings",
          url: "/agent/agentaaa/sessionccc",
        },
      ],
    },
    {
      title: "Agent 2",
      url: "/agent/agentbbb",
      items: [
        {
          title: "Genesis",
          url: "/agent/agentbbb/sessionaaa",
        },
        {
          title: "Explorer",
          url: "/agent/agentbbb/sessionbbb",
        },
        {
          title: "Quantum",
          url: "/agent/agentbbb/sessionccc",
        },
      ],
    },
    {
      title: "Agent 3",
      url: "/agent/agentccc",
      items: [
        {
          title: "Introduction",
          url: "/agent/agentccc/sessionaaa",
        },
        {
          title: "Get Started",
          url: "/agent/agentccc/sessionbbb",
        },
        {
          title: "Tutorials",
          url: "/agent/agentccc/sessionccc",
        },
        {
          title: "Changelog",
          url: "/agent/agentccc/sessionddd",
        },
      ],
    },
  ],
  navMainPersonal: [
    {
      title: "Agent 1",
      url: "/agent/agent111",
      isActive: true,
      items: [
        {
          title: "History",
          url: "/agent/agent111/session111",
        },
        {
          title: "Starred",
          url: "/agent/agent111/session222",
        },
        {
          title: "Settings",
          url: "/agent/agent111/session333",
        },
      ],
    },
    {
      title: "Agent 2",
      url: "/agent/agent222",
      items: [
        {
          title: "Genesis",
          url: "/agent/agent222/session111",
        },
        {
          title: "Explorer",
          url: "/agent/agent222/session222",
        },
        {
          title: "Quantum",
          url: "/agent/agent222/session333",
        },
      ],
    },
    {
      title: "Agent 3",
      url: "/agent/agent333",
      items: [
        {
          title: "Introduction",
          url: "/agent/agent333/session111",
        },
        {
          title: "Get Started",
          url: "/agent/agent333/session222",
        },
        {
          title: "Tutorials",
          url: "/agent/agent333/session444",
        },
        {
          title: "Changelog",
          url: "/agent/agent333/session333",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "模型配置",
      icon: Bot,
      url: "/models",
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [isAgentFormOpen, setIsAgentFormOpen] = useState(false)

  // 使用Hook获取状态
  const isAdmin = useIsTeamAdmin()
  const userData = useCurrentUser()

  // 处理添加 Agent
  const handleAddAgent = (data: any) => {
    console.log("添加 Agent:", data)
    // 这里可以添加实际的 Agent 创建逻辑
  }

  const allProjects = [
      {
        name: "团队成员",
        url: "/users",
        icon: UserRound,
      },
      {
        name: "数据集",
        url: "/datasets",
        icon: Database,
      },
      {
        name: "知识库",
        url: "/knowledge-base",
        icon: BookOpenText,
      },
      {
        name: "文件",
        url: "/files",
        icon: FileText,
      },
    ]

  // 获取次要导航项（仅管理员可见）
  const getSecondaryNavItems = () => {
    if (isAdmin) {
      return mockData.navSecondary
    }
    return []
  }

  return (
    <Sidebar collapsible="icon" variant="inset" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
        <Button onClick={() => setIsAgentFormOpen(true)}>
          <Plus/>
          添加Agent
        </Button>
      </SidebarHeader>
      <SidebarContent>
       {isAdmin && <NavProjects title={"团队管理"} projects={allProjects} />}
        <NavMain title={"个人Agent"} items={mockData.navMainTeam} />
        <NavMain title={"团队Agent"} items={mockData.navMainPersonal} />
        {getSecondaryNavItems().length > 0 && (
          <NavSecondary items={getSecondaryNavItems()} className="mt-auto" />
        )}
      </SidebarContent>
      <SidebarFooter>
        <NavUser
          user={userData ? {
            name: userData.nick || "用户",
            email: userData.username || "",
            avatar: userData.avatar || "",
          } : {
            name: "用户",
            email: "",
            avatar: "",
          }}
        />
      </SidebarFooter>
      <SidebarRail />

      {/* Agent 添加对话框 */}
      <AgentDialog
        open={isAgentFormOpen}
        onClose={() => setIsAgentFormOpen(false)}
        onSubmit={handleAddAgent}
      />
    </Sidebar>
  )
}
