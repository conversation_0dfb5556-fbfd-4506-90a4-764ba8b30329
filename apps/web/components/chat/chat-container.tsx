"use client"

import { useState, useRef, useEffect } from "react"
import { ChatMessage, type MessageRole } from "./chat-message"
import { ChatInput } from "./chat-input"
import { cn } from "@ragtop-web/ui/lib/utils"

export interface Message {
  id: string
  role: MessageRole
  content: string
  timestamp?: string
}

interface ChatContainerProps {
  initialMessages?: Message[]
  className?: string
  agentName?: string
}

export function ChatContainer({
  initialMessages = [],
  className,
  agentName = "助手"
}: ChatContainerProps) {
  const [messages, setMessages] = useState<Message[]>(initialMessages)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current
      scrollContainer.scrollTop = scrollContainer.scrollHeight
    }
  }, [messages])

  const handleSendMessage = (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content,
      timestamp: new Date().toISOString()
    }

    setMessages((prev) => [...prev, userMessage])

    // Simulate assistant response (in a real app, this would be an API call)
    setTimeout(() => {
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: "assistant",
        content: "你好！有什么我可以帮助你的吗？",
        timestamp: new Date().toISOString()
      }

      setMessages((prev) => [...prev, assistantMessage])
    }, 1000)
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      <div
        className="flex-1 overflow-y-auto"
        ref={scrollAreaRef}
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            开始新的对话...
          </div>
        ) : (
          <div>
            {messages.map((message) => (
              <ChatMessage
                key={message.id}
                role={message.role}
                content={message.content}
                timestamp={message.timestamp}
                agentName={agentName}
              />
            ))}
          </div>
        )}
      </div>

      <div className="mt-auto pt-4">
        <ChatInput onSend={handleSendMessage} />
      </div>
    </div>
  )
}
