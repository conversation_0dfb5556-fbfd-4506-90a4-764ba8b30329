"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog"

// 表单验证模式
const formSchema = z.object({
  currentPassword: z.string().min(1, "当前密码不能为空"),
  newPassword: z.string().min(6, "新密码至少需要6个字符"),
  confirmPassword: z.string().min(6, "确认密码至少需要6个字符"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "新密码与确认密码不匹配",
  path: ["confirmPassword"],
})

type FormValues = z.infer<typeof formSchema>

interface PasswordChangeDialogProps {
  open: boolean
  onClose: () => void
}

/**
 * 密码修改对话框组件
 */
export function ModelChangeDialog({ open, onClose }: PasswordChangeDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState("")

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  })

  // 处理密码修改
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true)
    setError("")
    setSuccess(false)

    // 模拟密码修改请求
    setTimeout(() => {
      // 简单的验证逻辑 - 在实际应用中应该调用API
      if (values.currentPassword === "password123") {
        // 保存新密码 (在实际应用中应该调用API)
        localStorage.setItem("adminPassword", values.newPassword)
        setSuccess(true)
        
        // 重置表单
        form.reset()
        
        // 3秒后关闭对话框
        setTimeout(() => {
          onClose()
          setSuccess(false)
        }, 3000)
      } else {
        setError("当前密码不正确")
      }
      setIsLoading(false)
    }, 1000)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置默认模型</DialogTitle>
          <DialogDescription>
            请选择需要的模型
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="p-3 bg-destructive/15 text-destructive rounded-md text-sm">
            {error}
          </div>
        )}
        
        {success && (
          <div className="p-3 bg-green-500/15 text-green-500 rounded-md text-sm">
            密码修改成功！对话框将自动关闭...
          </div>
        )}
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="currentPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>聊天模型</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="请选择适当的模型" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>嵌入模型</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="请选择适当的模型" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rerank模型</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="请选择适当的模型" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                取消
              </Button>
              <Button type="submit" disabled={isLoading || success}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    提交中...
                  </span>
                ) : (
                  "确认"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
