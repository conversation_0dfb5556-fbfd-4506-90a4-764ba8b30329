"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { isTeamAdmin } from "@/lib/user-role"

/**
 * 身份验证检查组件
 *
 * 检查用户是否已登录，如果未登录则重定向到登录页面
 * 同时根据用户角色限制对某些页面的访问
 */
export function AuthCheck({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查是否已登录
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true"

    // 如果未登录且不在登录页面，则重定向到登录页面
    if (!isLoggedIn && pathname !== "/login") {
      router.push("/login")
      return
    }

    // 如果已登录，检查用户角色和访问权限
    if (isLoggedIn && pathname !== "/login") {
      // 管理员专属页面列表（不包括 /users，因为所有用户都可以访问）
      const adminOnlyPages = ["/models"]

      // 检查是否访问的是管理员专属页面
      const isAdminPage = adminOnlyPages.some(page => pathname === page || pathname.startsWith(`${page}/`))

      if (isAdminPage) {
        // 检查用户是否是管理员
        const isAdmin = isTeamAdmin()

        if (!isAdmin) {
          // 如果不是管理员但尝试访问管理员页面，重定向到首页
          router.push("/home")
          return
        }
      }
    }

    setIsLoading(false)
  }, [router, pathname])

  // 如果正在加载，则显示加载状态
  if (isLoading && pathname !== "/login") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-current border-t-transparent"></div>
          <p className="text-sm text-muted-foreground">正在验证身份...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
