"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { Flexibility } from "@/service/agent-service"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@ragtop-web/ui/components/form"
import { Input } from "@ragtop-web/ui/components/input"
import { Textarea } from "@ragtop-web/ui/components/textarea"
import { Button } from "@ragtop-web/ui/components/button"
import { Switch } from "@ragtop-web/ui/components/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@ragtop-web/ui/components/select"
import { Slider } from "@ragtop-web/ui/components/slider"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@ragtop-web/ui/components/dialog"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@ragtop-web/ui/components/tabs"
import { Label } from "@ragtop-web/ui/components/label"
import { Separator } from "@ragtop-web/ui/components/separator"

// Agent 表单数据类型
export interface AgentFormData {
  // 助理设置
  name: string
  description: string
  emptyReply?: string
  hello_message?: string
  showCitation: boolean
  refineMultiturn: boolean
  reasoning: boolean
  resourceType: "knowledgeBase" | "dataset" // 资源类型：知识库或数据集
  resource: string // 选择的资源ID
  agentPermission: "personal" | "team" // Agent权限：个人或团队

  // 提示引擎
  systemPrompt: string
  similarityThreshold: number
  keywordWeight: number
  topN: number
  reRankModel?: string
  keyword?: string[]

  // 模型设置
  model: string
  temperature: number
  topP: number
  presencePenalty: number
  frequencyPenalty: number
  freedom: Flexibility // 自由度
}

// 默认表单数据
const defaultFormData: AgentFormData = {
  name: "",
  description: "",
  emptyReply: "",
  hello_message: "你好！我是你的助手，有什么可以帮到你的吗？",
  showCitation: true,
  refineMultiturn: false,
  reasoning: false,
  resourceType: "knowledgeBase", // 默认资源类型为"知识库"
  resource: "", // 默认资源为空
  agentPermission: "personal", // 默认Agent权限为"个人"
  systemPrompt: "",
  similarityThreshold: 0.5,
  keywordWeight: 0.5,
  topN: 5,
  reRankModel: "none",
  keyword: [],
  model: "gpt-4",
  temperature: 0.1,
  topP: 0.3,
  presencePenalty: 0.4,
  frequencyPenalty: 0.7,
  freedom: Flexibility.Deliberate // 默认自由度为"精确"(Deliberate)
}

interface AgentDialogProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: AgentFormData) => void
  initialData?: AgentFormData
}

/**
 * Agent 对话框组件，包含三个标签页：助理设置、提示引擎、模型设置
 */
export function AgentDialog({ open, onClose, onSubmit, initialData }: AgentDialogProps) {

  const form = useForm<AgentFormData>({
    defaultValues: initialData || defaultFormData
  })

  const resourceType = form.watch("resourceType");

  // 处理表单提交
  const handleSubmit = (data: AgentFormData) => {
    onSubmit(data)
    onClose()
  }


  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>聊天配置</DialogTitle>
          <p className="text-sm text-muted-foreground">在这里，为你的专业知识库装扮专属助手！</p>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="resourceType"
              rules={{ required: "请选择资源类型" }}
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="after:content-['*'] after:ml-0.5">资源类型</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-6">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="knowledgeBase"
                          value="knowledgeBase"
                          checked={field.value === "knowledgeBase"}
                          onChange={() => field.onChange("knowledgeBase")}
                          className="h-4 w-4 text-primary border-input focus:ring-primary"
                        />
                        <Label htmlFor="knowledgeBase" className="cursor-pointer">知识库</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="dataset"
                          value="dataset"
                          checked={field.value === "dataset"}
                          onChange={() => field.onChange("dataset")}
                          className="h-4 w-4 text-primary border-input focus:ring-primary"
                        />
                        <Label htmlFor="dataset" className="cursor-pointer">数据集</Label>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="resource"
              rules={{ required: "请选择资源" }}
              render={({ field }) => {

                return (
                  <FormItem>
                    <FormLabel className="after:content-['*'] after:ml-0.5">
                      {resourceType === "knowledgeBase" ? "知识库" : "数据集"}
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder={`请选择${resourceType === "knowledgeBase" ? "知识库" : "数据集"}`} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {resourceType === "knowledgeBase" ? (
                          // 知识库选项
                          <>
                            <SelectItem value="kb1">知识库1</SelectItem>
                            <SelectItem value="kb2">知识库2</SelectItem>
                            <SelectItem value="kb3">知识库3</SelectItem>
                            <SelectItem value="kb4">知识库4</SelectItem>
                            <SelectItem value="kb5">知识库5</SelectItem>
                            <SelectItem value="kb6">知识库6</SelectItem>
                          </>
                        ) : (
                          // 数据集选项
                          <>
                            <SelectItem value="ds1">数据集1</SelectItem>
                            <SelectItem value="ds2">数据集2</SelectItem>
                            <SelectItem value="ds3">数据集3</SelectItem>
                            <SelectItem value="ds4">数据集4</SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <Separator />
            <Tabs defaultValue="assistant-settings" className="w-full">
              <TabsList className={`w-full grid grid-cols-${resourceType === "knowledgeBase" ? 3 : 2} mb-4`}>
                <TabsTrigger value="assistant-settings">基础设置</TabsTrigger>
                <TabsTrigger value="prompt-engine">提示引擎</TabsTrigger>
                {resourceType === "knowledgeBase" && <TabsTrigger value="model-settings">模型设置</TabsTrigger>}
              </TabsList>

              {/* 助理设置标签页 */}
              <TabsContent value="assistant-settings" className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  rules={{ required: "Agent名称不能为空" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="after:content-['*'] after:ml-0.5">Agent名称</FormLabel>
                      <FormControl>
                        <Input placeholder="测试Agent" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Agent描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="你是一个专业的商务助手，只需回答商务的问题。"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="emptyReply"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>空回复</FormLabel>
                      <FormControl>
                        <Input placeholder="抱歉，我无法回答这个问题。" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hello_message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>设置开场白</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="你好！我是你的助手，有什么可以帮到你的吗？"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="agentPermission"
                  rules={{ required: "请选择Agent权限" }}
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="after:content-['*'] after:ml-0.5">Agent权限</FormLabel>
                      <FormControl>
                        <div className="flex items-center space-x-6">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="personal"
                              value="personal"
                              checked={field.value === "personal"}
                              onChange={() => field.onChange("personal")}
                              className="h-4 w-4 text-primary border-input focus:ring-primary"
                            />
                            <Label htmlFor="personal" className="cursor-pointer">个人</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="team"
                              value="team"
                              checked={field.value === "team"}
                              onChange={() => field.onChange("team")}
                              className="h-4 w-4 text-primary border-input focus:ring-primary"
                            />
                            <Label htmlFor="team" className="cursor-pointer">团队</Label>
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />


              </TabsContent>

              {/* 提示引擎标签页 */}
              <TabsContent value="prompt-engine" className="space-y-4">
                <FormField
                  control={form.control}
                  name="systemPrompt"
                  rules={{ required: "系统提示词不能为空" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="after:content-['*'] after:ml-0.5">系统提示词</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="你是一个学术领域的专家，请根据知识库的内容来尽可能详细的回答问题。
以下是知识库：
{knowledge}
以上是知识库。"
                          className="min-h-32"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {resourceType === "knowledgeBase" && <>
                  <FormField
                    control={form.control}
                    name="similarityThreshold"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>相似度阈值</FormLabel>
                          <span className="text-sm">{field.value}</span>
                        </div>
                        <FormControl>
                          <Slider
                            value={[field.value]}
                            min={0}
                            max={1}
                            step={0.01}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="keywordWeight"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>关键字相似度权重</FormLabel>
                          <span className="text-sm">{field.value}</span>
                        </div>
                        <FormControl>
                          <Slider
                            value={[field.value]}
                            min={0}
                            max={1}
                            step={0.01}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="topN"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>Top N</FormLabel>
                          <span className="text-sm">{field.value}</span>
                        </div>
                        <FormControl>
                          <Slider
                            value={[field.value]}
                            min={1}
                            max={20}
                            step={1}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                                  <FormField
                  control={form.control}
                  name="refineMultiturn"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <FormLabel>多轮对话优化</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                                <FormField
                  control={form.control}
                  name="reasoning"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <FormLabel>推理</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                  </>}

              </TabsContent>

              {/* 模型设置标签页 */}
              <TabsContent value="model-settings" className="space-y-4">
                <FormField
                  control={form.control}
                  name="model"
                  rules={{ required: "请选择模型" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="after:content-['*'] after:ml-0.5">模型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="请选择模型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="gpt-4">GPT-4</SelectItem>
                          <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                          <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                          <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                          <SelectItem value="llama-3">Llama 3</SelectItem>
                          <SelectItem value="deepseek-r1">deepseek-r1</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="freedom"
                  rules={{ required: "请选择自由度" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="after:content-['*'] after:ml-0.5">自由度</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="请选择自由度" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={Flexibility.Improvised}>即兴创作 (Improvised)</SelectItem>
                          <SelectItem value={Flexibility.Deliberate}>精确 (Deliberate)</SelectItem>
                          <SelectItem value={Flexibility.Balanced}>平衡 (Balanced)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="temperature"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>温度</FormLabel>
                        <span className="text-sm">{field.value}</span>
                      </div>
                      <FormControl>
                        <Slider
                          value={[field.value]}
                          min={0}
                          max={1}
                          step={0.01}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="topP"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>Top P</FormLabel>
                        <span className="text-sm">{field.value}</span>
                      </div>
                      <FormControl>
                        <Slider
                          value={[field.value]}
                          min={0}
                          max={1}
                          step={0.01}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button type="submit">
                确定
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
