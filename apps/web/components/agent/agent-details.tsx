"use client"

import { useState } from "react"
import { Pencil } from "lucide-react"
import { Button } from "@ragtop-web/ui/components/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@ragtop-web/ui/components/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card"
import { Badge } from "@ragtop-web/ui/components/badge"
import { AgentDialog, type AgentFormData } from "./agent-dialog"
import { Flexibility } from "@/service/agent-service"

interface AgentDetailsProps {
  agent: AgentFormData
  onUpdate?: (data: AgentFormData) => void
}

/**
 * Agent详情组件，展示Agent的所有信息，包括助理设置、提示引擎和模型设置
 */
export function AgentDetails({ agent, onUpdate }: AgentDetailsProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  // 处理编辑Agent
  const handleEditAgent = (data: AgentFormData) => {
    if (onUpdate) {
      onUpdate(data)
    }
    setIsEditDialogOpen(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <Button onClick={() => setIsEditDialogOpen(true)}>
          <Pencil className="h-4 w-4 mr-2" />
          编辑
        </Button>
      </div>

      <Tabs defaultValue="assistant-settings" className="w-full">
        <TabsList className={`w-full grid ${agent.resourceType === "knowledgeBase" ? "grid-cols-3" : "grid-cols-2"} mb-4`}>
          <TabsTrigger value="assistant-settings">助理设置</TabsTrigger>
          <TabsTrigger value="prompt-engine">提示引擎</TabsTrigger>
          {agent.resourceType === "knowledgeBase" && <TabsTrigger value="model-settings">模型设置</TabsTrigger>}
        </TabsList>

        {/* 助理设置标签页 */}
        <TabsContent value="assistant-settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Agent姓名</h3>
                  <p>{agent.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">资源类型</h3>
                  <p>{agent.resourceType === "knowledgeBase" ? "知识库" : "数据集"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">{agent.resourceType === "knowledgeBase" ? "知识库" : "数据集"}</h3>
                  <p>{agent.resource}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Agent权限</h3>
                  <div className="flex items-center space-x-6 mt-1">
                    <div className="flex items-center space-x-2">
                      <div className={`h-4 w-4 rounded-full border ${agent.agentPermission === "personal" ? "bg-primary border-primary" : "bg-background border-input"}`}>
                        {agent.agentPermission === "personal" && (
                          <div className="h-2 w-2 mx-auto mt-0.5 rounded-full bg-white"></div>
                        )}
                      </div>
                      <span className={agent.agentPermission === "personal" ? "font-medium" : ""}>个人</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`h-4 w-4 rounded-full border ${agent.agentPermission === "team" ? "bg-primary border-primary" : "bg-background border-input"}`}>
                        {agent.agentPermission === "team" && (
                          <div className="h-2 w-2 mx-auto mt-0.5 rounded-full bg-white"></div>
                        )}
                      </div>
                      <span className={agent.agentPermission === "team" ? "font-medium" : ""}>团队</span>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Agent描述</h3>
                <p>{agent.description || "无描述"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">开场白</h3>
                <p>{agent.hello_message}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">空回复</h3>
                <p>{agent.emptyReply || "无设置"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">显示引用</h3>
                <Badge variant={agent.showCitation ? "default" : "secondary"}>
                  {agent.showCitation ? "是" : "否"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 提示引擎标签页 */}
        <TabsContent value="prompt-engine" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>提示词设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">系统提示词</h3>
                <pre className="p-4 bg-muted rounded-md whitespace-pre-wrap text-sm">
                  {agent.systemPrompt}
                </pre>
              </div>
            </CardContent>
          </Card>

          {agent.resourceType === "knowledgeBase" && (
            <Card>
              <CardHeader>
                <CardTitle>检索设置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">相似度阈值</h3>
                    <p>{agent.similarityThreshold}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">关键词权重</h3>
                    <p>{agent.keywordWeight}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">检索数量</h3>
                    <p>{agent.topN}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">重排模型</h3>
                    <p>{agent.reRankModel || "无"}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>高级设置</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">多轮对话优化</h3>
                  <Badge variant={agent.refineMultiturn ? "default" : "secondary"}>
                    {agent.refineMultiturn ? "开启" : "关闭"}
                  </Badge>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">推理</h3>
                  <Badge variant={agent.reasoning ? "default" : "secondary"}>
                    {agent.reasoning ? "开启" : "关闭"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 模型设置标签页 - 仅在知识库类型时显示 */}
        {agent.resourceType === "knowledgeBase" && (
          <TabsContent value="model-settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>模型配置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">模型</h3>
                    <p>{agent.model}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">温度</h3>
                    <p>{agent.temperature}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Top P</h3>
                    <p>{agent.topP}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">存在惩罚</h3>
                    <p>{agent.presencePenalty}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">频率惩罚</h3>
                    <p>{agent.frequencyPenalty}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">自由度</h3>
                    <p>
                      {agent.freedom === Flexibility.Improvised && "即兴创作 (Improvised)"}
                      {agent.freedom === Flexibility.Deliberate && "精确 (Deliberate)"}
                      {agent.freedom === Flexibility.Balanced && "平衡 (Balanced)"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>

      {/* Agent 编辑对话框 */}
      <AgentDialog
        open={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSubmit={handleEditAgent}
        initialData={agent}
      />
    </div>
  )
}
