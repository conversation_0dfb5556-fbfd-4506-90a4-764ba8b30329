"use client"

import * as React from "react"
import { ChevronsUpDown, Loader2, UsersRound } from "lucide-react"
import { useAtom, useSetAtom } from 'jotai'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@ragtop-web/ui/components/sidebar"
import { Badge } from "@ragtop-web/ui/components/badge"
import { useTeams } from "@/service/team-service"
import { currentTeamAtom, setCurrentTeamAtom, teamIdAtom } from "@/store/team-store"
import { useIsTeamAdmin } from "@/lib/user-role"

const getRoles=(roles?:string[])=>{
  if(roles?.includes("TEAM_ADMIN")){
    return "管理员"
  }else {
    return "成员"
  }
}

export function TeamSwitcher() {
  const { isMobile } = useSidebar()
  const { data, isLoading } = useTeams()
  const [currentTeam, setCurrentTeam] = useAtom(currentTeamAtom)
  const setTeam = useSetAtom(setCurrentTeamAtom)
  const [teamId] = useAtom(teamIdAtom)

  // 使用 useEffect 确保在数据加载完成后再设置 activeTeam
  React.useEffect(() => {
    if (data && data.length > 0) {
      const team = data.find(team => team.id === teamId) || data[0]
      if (team) {
        setTeam(team)
      }
    }
  }, [data, teamId, setTeam])

  // 如果正在加载或没有数据，显示加载状态
  if (isLoading) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg">
            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
              <Loader2 className="size-4 animate-spin" />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">加载中...</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  // 如果没有团队数据或当前团队，不显示任何内容
  if (!data?.length || !currentTeam) {
    return null
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <UsersRound className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{currentTeam.title}</span>
                <Badge variant="secondary">{getRoles(currentTeam.roles)}</Badge>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              所属团队
            </DropdownMenuLabel>
            {data?.map((team) => (
              <DropdownMenuItem
                key={team.id}
                onClick={() => {
                  // 使用Jotai设置当前团队
                  setTeam(team)
                  // 刷新页面以更新权限
                  window.location.href = "/"
                }}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  <UsersRound className="size-3.5 shrink-0" />
                </div>
                {team.title}
                <DropdownMenuShortcut>{getRoles(team.roles)}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
