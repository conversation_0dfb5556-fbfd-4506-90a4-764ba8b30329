"use client"

import { useState, useEffect } from "react"
import { ChevronRight, SquarePen, Eraser, MoreHorizontal, Trash2, MessageSquarePlus, BotMessageSquare, type LucideIcon } from "lucide-react"
import { usePathname } from "next/navigation"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@ragtop-web/ui/components/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar
} from "@ragtop-web/ui/components/sidebar"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator
} from "@ragtop-web/ui/components/dropdown-menu"
import { Button } from "@ragtop-web/ui/components/button"
import Link from "next/link"
import { AgentDialog } from "@/components/agent/agent-dialog"
import { Flexibility } from "@/service/agent-service"

// 模拟Agent数据
const mockAgentData = {
  name: "测试助理",
  description: "这是一个测试助理，用于展示Agent详情页面",
  avatar: null,
  emptyReply: "抱歉，我无法回答这个问题。",
  hello_message: "你好！我是你的助手，有什么可以帮到你的吗？",
  showCitation: true,
  refineMultiturn: false,
  reasoning: false,
  resourceType: "knowledgeBase" as "knowledgeBase", // 资源类型默认为"知识库"
  resource: "kb1", // 选择的资源ID
  agentPermission: "personal" as "personal", // Agent权限默认为"个人"
  systemPrompt: "你是一个学术领域的专家，请根据知识库的内容来尽可能详细的回答问题。\n以下是知识库：\n{knowledge}\n以上是知识库。",
  similarityThreshold: 0.5,
  keywordWeight: 0.5,
  topN: 5,
  reRankModel: "none",
  keyword: [],
  model: "gpt-4",
  temperature: 0.1,
  topP: 0.3,
  presencePenalty: 0.4,
  frequencyPenalty: 0.7,
  freedom: Flexibility.Deliberate
}

export function NavMain({
  title,
  items,
  onDeleteSubItem,
}: {
  title: string
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
      id?: string
    }[]
  }[]
  onDeleteSubItem?: (itemTitle: string, subItemTitle: string, subItemId?: string) => void
}) {
  const { isMobile } = useSidebar()
  const pathname = usePathname()
  const [isAgentEditDialogOpen, setIsAgentEditDialogOpen] = useState(false)
  const [currentAgent, setCurrentAgent] = useState<string | null>(null)
  const [activeItems, setActiveItems] = useState<string[]>([])

  // 根据当前路径设置选中状态
  useEffect(() => {
    // 检查当前路径是否为详情页面或会话页面
    const pathSegments = pathname.split('/')
    if (pathSegments.length >= 3 && pathSegments[1] === 'agent') {
      const agentId = pathSegments[2]

      // 找到匹配的 agent 项
      const matchingItems = items.filter(item => {
        const itemPathSegments = item.url.split('/')
        return itemPathSegments.length >= 3 && itemPathSegments[2] === agentId
      })

      setActiveItems(matchingItems.map(item => item.title))
    } else {
      setActiveItems([])
    }
  }, [pathname, items])

  // 处理编辑Agent
  const handleEditAgent = (agentTitle: string) => {
    setCurrentAgent(agentTitle)
    setIsAgentEditDialogOpen(true)
  }

  // 处理更新Agent
  const handleUpdateAgent = (data: any) => {
    console.log(`更新Agent "${currentAgent}":`, data)
    // 这里可以添加实际的Agent更新逻辑
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible
            key={item.title}
            asChild
            defaultOpen={item.isActive || activeItems.includes(item.title)}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                data-active={activeItems.includes(item.title) || pathname === item.url}
              >
                <Link href={item.url}>
                  <BotMessageSquare />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
              <CollapsibleTrigger asChild>
                <SidebarMenuAction
                  className="left-2 bg-sidebar-accent text-sidebar-accent-foreground data-[state=open]:rotate-90"
                  showOnHover
                >
                  <ChevronRight />
                </SidebarMenuAction>
              </CollapsibleTrigger>
              <SidebarMenuAction showOnHover>
                <MessageSquarePlus className="mr-10 h-4 w-4 text-muted-foreground" />
                  </SidebarMenuAction>
              <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                  <SidebarMenuAction showOnHover>
                    <MoreHorizontal />
                    <span className="sr-only">More</span>
                  </SidebarMenuAction>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-48 rounded-lg"
                  side={isMobile ? "bottom" : "right"}
                  align={isMobile ? "end" : "start"}
                >
                  <DropdownMenuItem>
                    <Eraser className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>清空会话</span>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleEditAgent(item.title)}>
                    <SquarePen className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>编辑Agent</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Trash2 className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>删除Agent</span>
                  </DropdownMenuItem>

                </DropdownMenuContent>
              </DropdownMenu>
              <CollapsibleContent>
                <SidebarMenuSub>
                  {item.items?.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.title} className="group/sub-item relative">
                      <SidebarMenuSubButton
                        asChild
                        data-active={pathname === subItem.url}
                      >
                        <Link href={subItem.url}>
                          <span>{subItem.title}</span>
                        </Link>
                      </SidebarMenuSubButton>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover/sub-item:opacity-100 transition-opacity h-6 w-6 p-0.5 text-destructive hover:bg-destructive/10 hover:text-destructive"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          onDeleteSubItem?.(item.title, subItem.title, subItem.id);
                        }}
                        aria-label={`删除 ${subItem.title}`}
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                        <span className="sr-only">删除 {subItem.title}</span>
                      </Button>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>

      {/* Agent 编辑对话框 */}
      <AgentDialog
        open={isAgentEditDialogOpen}
        onClose={() => setIsAgentEditDialogOpen(false)}
        onSubmit={handleUpdateAgent}
        initialData={mockAgentData}
      />
    </SidebarGroup>
  )
}

