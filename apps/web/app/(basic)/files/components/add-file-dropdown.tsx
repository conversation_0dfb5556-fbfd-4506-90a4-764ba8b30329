"use client"

import { PlusIcon, UploadIcon, FolderPlusIcon } from "lucide-react"
import { Button } from "@ragtop-web/ui/components/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu"

interface AddFileDropdownProps {
  onUploadFile: () => void
  onCreateFolder: () => void
}

/**
 * 新增文件下拉菜单组件
 */
export function AddFileDropdown({ onUploadFile, onCreateFolder }: AddFileDropdownProps) {
  return (
     <Button onClick={onUploadFile}>
          <UploadIcon className="h-4 w-4" />
          上传文件
        </Button>
  )
  // return (
  //   <DropdownMenu>
  //     <DropdownMenuTrigger asChild>
  //       <Button>
  //         <PlusIcon className="h-4 w-4 mr-2" />
  //         新增文件
  //       </Button>
  //     </DropdownMenuTrigger>
  //     <DropdownMenuContent align="end">
  //       <DropdownMenuItem onClick={onUploadFile}>
  //         <UploadIcon className="h-4 w-4 mr-2" />
  //         上传文件
  //       </DropdownMenuItem>
  //       <DropdownMenuItem onClick={onCreateFolder}>
  //         <FolderPlusIcon className="h-4 w-4 mr-2" />
  //         新建文件夹
  //       </DropdownMenuItem>
  //     </DropdownMenuContent>
  //   </DropdownMenu>
  // )
}
