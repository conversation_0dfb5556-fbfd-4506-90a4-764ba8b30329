"use client"

import { MoreHorizontal, Link, PencilIcon, MoveIcon, Trash2 } from "lucide-react"
import { Button } from "@ragtop-web/ui/components/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ragtop-web/ui/components/dropdown-menu"
import { FileItem } from "../types"

interface FileActionMenuProps {
  file: FileItem
  onLinkKnowledgeBase: (file: FileItem) => void
  onRename: (file: FileItem) => void
  onMove: (file: FileItem) => void
  onDelete: (file: FileItem) => void
}

/**
 * 文件操作菜单组件
 */
export function FileActionMenu({
  file,
  onLinkKnowledgeBase,
  onRename,
  onMove,
  onDelete,
}: FileActionMenuProps) {
  // 空文件夹不显示操作按钮
  const isEmptyFolder = file.isFolder && file.size === 0

  if (isEmptyFolder) {
    return null
  }

  return (
    <div className="flex gap-2">
      {/* 链接知识库：本期先不做 */}
        {/* {!file.isFolder && (
          <Button size={"icon"} variant={"ghost"} onClick={() => onLinkKnowledgeBase(file)}>
            <Link className="h-4 w-4 " />
          </Button>
        )} */}
        <Button size={"icon"} variant={"ghost"} onClick={() => onRename(file)}>
          <PencilIcon className="h-4 w-4" />
          {/* 重命名 */}
        </Button>
        {/* <Button onClick={() => onMove(file)}>
          <MoveIcon className="h-4 w-4 mr-2" />
          移动
        </Button> */}
        <Button 
        size={"icon"} variant={"ghost"}
          onClick={() => onDelete(file)}
          className="text-destructive focus:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
          {/* 删除 */}
        </Button>
    </div>
  )
}
