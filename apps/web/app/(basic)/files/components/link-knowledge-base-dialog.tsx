"use client"

import { useState } from "react"
import { <PERSON> } from "lucide-react"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogT<PERSON>le,
  DialogFooter,
} from "@ragtop-web/ui/components/dialog"
import { Button } from "@ragtop-web/ui/components/button"
import { Checkbox } from "@ragtop-web/ui/components/checkbox"
import { Label } from "@ragtop-web/ui/components/label"
import { ScrollArea } from "@ragtop-web/ui/components/scroll-area"
import { KnowledgeBaseReference } from "../types"

interface LinkKnowledgeBaseDialogProps {
  open: boolean
  onClose: () => void
  onLink: (knowledgeBaseIds: string[]) => void
  fileName: string
  knowledgeBases: KnowledgeBaseReference[]
  linkedKnowledgeBaseIds: string[]
}

/**
 * 链接知识库对话框组件
 */
export function LinkKnowledgeBaseDialog({
  open,
  onClose,
  onLink,
  fileName,
  knowledgeBases,
  linkedKnowledgeBaseIds,
}: LinkKnowledgeBaseDialogProps) {
  const [selectedIds, setSelectedIds] = useState<string[]>(linkedKnowledgeBaseIds)

  // 处理选择变更
  const handleToggle = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id)
        ? prev.filter((item) => item !== id)
        : [...prev, id]
    )
  }

  // 处理提交
  const handleSubmit = () => {
    onLink(selectedIds)
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>链接知识库</DialogTitle>
        </DialogHeader>
        <div className="py-2">
          <p className="text-sm text-muted-foreground mb-4">
            选择要链接到文件 <span className="font-medium">{fileName}</span> 的知识库
          </p>
          <ScrollArea className="h-[300px] rounded-md border p-4">
            <div className="space-y-4">
              {knowledgeBases.map((kb) => (
                <div key={kb.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={kb.id}
                    checked={selectedIds.includes(kb.id)}
                    onCheckedChange={() => handleToggle(kb.id)}
                  />
                  <Label htmlFor={kb.id} className="cursor-pointer">
                    {kb.name}
                  </Label>
                </div>
              ))}
              {knowledgeBases.length === 0 && (
                <p className="text-sm text-muted-foreground">暂无可用的知识库</p>
              )}
            </div>
          </ScrollArea>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={handleSubmit}>
            <Link className="mr-2 h-4 w-4" />
            确认链接
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
