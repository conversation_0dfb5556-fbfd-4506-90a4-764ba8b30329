"use client"

import { useState, useRef } from "react"
import { Upload, X } from "lucide-react"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import { Progress } from "@ragtop-web/ui/components/progress"
import { formatFileSize } from "../utils"

interface FileUploadProps {
  onUploadComplete: (formData: FormData) => void
}

export function FileUpload({ onUploadComplete }: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    setSelectedFile(file)
    setUploadProgress(0)
  }

  // 处理文件上传
  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)

    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', selectedFile)

    // 模拟上传进度 (实际上传时应该使用XMLHttpRequest的progress事件)
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        const newProgress = prev + Math.random() * 10
        return newProgress >= 100 ? 100 : newProgress
      })
    }, 200)

    try {
      // 调用父组件的上传方法
      onUploadComplete(formData)

      // 清理
      clearInterval(interval)
      setUploadProgress(100)

      // 延迟重置UI状态
      setTimeout(() => {
        setSelectedFile(null)
        setUploadProgress(0)
        setIsUploading(false)

        // 清空文件输入
        if (fileInputRef.current) {
          fileInputRef.current.value = ""
        }
      }, 500)
    } catch (error) {
      console.error("Upload failed:", error)
      clearInterval(interval)
      setIsUploading(false)
    }
  }

  // 取消上传
  const handleCancel = () => {
    setSelectedFile(null)
    setUploadProgress(0)
    setIsUploading(false)

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        {!selectedFile ? (
          <>
            <Button
              type="button"
              variant="outline"
              className="flex gap-2"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-4 w-4" />
              选择文件
            </Button>
            <Input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={handleFileChange}
            />
          </>
        ) : (
          <div className="w-full space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium">{selectedFile.name}</span>
                <span className="text-sm text-muted-foreground">
                  ({formatFileSize(selectedFile.size)})
                </span>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={handleCancel}
                disabled={isUploading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Progress value={uploadProgress} className="flex-1" />
              <span className="text-sm text-muted-foreground w-10">
                {Math.round(uploadProgress)}%
              </span>
            </div>

            <div className="flex justify-end gap-2">
              {!isUploading ? (
                <Button
                  type="button"
                  onClick={handleUpload}
                >
                  上传文件
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                >
                  取消
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
