import { FileItem, KnowledgeBaseReference } from "./types"

/**
 * 模拟知识库数据
 */
export const mockKnowledgeBases: KnowledgeBaseReference[] = [
  { id: "kb1", name: "产品知识库" },
  { id: "kb2", name: "技术知识库" },
  { id: "kb3", name: "用户支持知识库" }
]

/**
 * 模拟文件数据
 */
export const mockFiles: FileItem[] = [
  // 文件夹
  {
    id: "folder1",
    name: "lalala",
    type: "Folder",
    size: 0,
    references: 0,
    createdAt: "2024-05-16T10:55:51Z",
    isFolder: true,
    parentId: null,
    path: "/lalala"
  },
  {
    id: "folder2",
    name: "knowledgebase",
    type: "Folder",
    size: 0,
    references: 0,
    createdAt: "2024-05-08T11:29:52Z",
    isFolder: true,
    parentId: null,
    path: "/knowledgebase"
  },
  // 文件
  {
    id: "1",
    name: "产品说明书.pdf",
    type: "PDF",
    size: 2515968, // 2.4 MB
    references: 2,
    createdAt: "2024-05-01T10:30:00Z",
    isFolder: false,
    parentId: null,
    path: "/产品说明书.pdf"
  },
  {
    id: "2",
    name: "用户手册.pdf",
    type: "PDF",
    size: 3250585, // 3.1 MB
    references: 1,
    createdAt: "2024-05-02T14:15:00Z",
    isFolder: false,
    parentId: null,
    path: "/用户手册.pdf"
  },
  {
    id: "3",
    name: "技术文档.pdf",
    type: "PDF",
    size: 1887436, // 1.8 MB
    references: 1,
    createdAt: "2024-05-03T09:45:00Z",
    isFolder: false,
    parentId: "folder1",
    path: "/lalala/技术文档.pdf"
  },
  {
    id: "4",
    name: "API文档.pdf",
    type: "PDF",
    size: 4404019, // 4.2 MB
    references: 1,
    createdAt: "2024-05-04T16:20:00Z",
    isFolder: false,
    parentId: "folder1",
    path: "/lalala/API文档.pdf"
  },
  {
    id: "5",
    name: "安装指南.pdf",
    type: "PDF",
    size: 1572864, // 1.5 MB
    references: 0,
    createdAt: "2024-05-05T11:10:00Z",
    isFolder: false,
    parentId: "folder2",
    path: "/knowledgebase/安装指南.pdf"
  },
  {
    id: "6",
    name: "常见问题.pdf",
    type: "PDF",
    size: 943718, // 0.9 MB
    references: 1,
    createdAt: "2024-05-06T13:25:00Z",
    isFolder: false,
    parentId: "folder2",
    path: "/knowledgebase/常见问题.pdf"
  },
  {
    id: "7",
    name: "系统架构.pdf",
    type: "PDF",
    size: 2831155, // 2.7 MB
    references: 1,
    createdAt: "2024-05-07T15:40:00Z",
    isFolder: false,
    parentId: null,
    path: "/系统架构.pdf"
  },
  {
    id: "8",
    name: "更新日志.txt",
    type: "Text",
    size: 1258291, // 1.2 MB
    references: 0,
    createdAt: "2024-05-08T10:05:00Z",
    isFolder: false,
    parentId: null,
    path: "/更新日志.txt"
  },
  {
    id: "9",
    name: "数据字典.xlsx",
    type: "Excel",
    size: 3670016, // 3.5 MB
    references: 0,
    createdAt: "2024-05-09T14:30:00Z",
    isFolder: false,
    parentId: null,
    path: "/数据字典.xlsx"
  },
  {
    id: "10",
    name: "安全白皮书.docx",
    type: "Word",
    size: 2202009, // 2.1 MB
    references: 0,
    createdAt: "2024-05-10T09:15:00Z",
    isFolder: false,
    parentId: null,
    path: "/安全白皮书.docx"
  }
]

/**
 * 获取文件的知识库引用
 */
export const getFileReferences = (fileId: string): KnowledgeBaseReference[] => {
  const file = mockFiles.find(f => f.id === fileId)
  if (!file || file.references === 0) return []

  // 模拟返回引用的知识库
  return mockKnowledgeBases.slice(0, file.references)
}
