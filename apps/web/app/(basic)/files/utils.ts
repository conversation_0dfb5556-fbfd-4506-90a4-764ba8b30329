/**
 * 格式化文件大小
 * @param bytes 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${units[i]}`
}

/**
 * 从文件名获取文件类型
 * @param filename 文件名
 * @returns 文件类型
 */
export function getFileType(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase() || ''
  
  // 常见文件类型映射
  const typeMap: Record<string, string> = {
    'pdf': 'PDF',
    'doc': 'Word',
    'docx': 'Word',
    'xls': 'Excel',
    'xlsx': 'Excel',
    'ppt': 'PowerPoint',
    'pptx': 'PowerPoint',
    'txt': 'Text',
    'csv': 'CSV',
    'jpg': 'Image',
    'jpeg': 'Image',
    'png': 'Image',
    'gif': 'Image',
    'zip': 'Archive',
    'rar': 'Archive',
    'mp4': 'Video',
    'mp3': 'Audio'
  }
  
  return typeMap[extension] || extension.toUpperCase() || 'Unknown'
}

/**
 * 获取文件图标颜色
 * @param fileType 文件类型
 * @returns 颜色类名
 */
export function getFileIconColor(fileType: string): string {
  const colorMap: Record<string, string> = {
    'PDF': 'text-red-500',
    'Word': 'text-blue-500',
    'Excel': 'text-green-500',
    'PowerPoint': 'text-orange-500',
    'Text': 'text-gray-500',
    'CSV': 'text-green-400',
    'Image': 'text-purple-500',
    'Archive': 'text-yellow-500',
    'Video': 'text-pink-500',
    'Audio': 'text-indigo-500'
  }
  
  return colorMap[fileType] || 'text-gray-400'
}
