"use client"

import { useState, useEffect } from "react"
import { use<PERSON>outer } from "next/navigation"
import { <PERSON>, Pencil, Search, Database } from "lucide-react"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import { Badge } from "@ragtop-web/ui/components/badge"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@ragtop-web/ui/components/dialog"
import { Textarea } from "@ragtop-web/ui/components/textarea"
import { Separator } from "@ragtop-web/ui/components/separator"
import { type Dataset } from "../page"
import { formatDate } from "@/lib/utils"
import { TableTree, type TableSchema, type TableItem } from "./components/table-tree"
import { TableBreadcrumb } from "./components/table-breadcrumb"

// 模拟数据集数据
const initialDatasets = [
  {
    id: "1",
    databaseName: "users_db",
    username: "admin",
    password: "********",
    database: "MySQL",
    buildVersion: "1.0.2",
    buildingVersion: "1.0.3",
    createdAt: "2023-10-15T08:30:00Z",
  },
  {
    id: "2",
    databaseName: "products_db",
    username: "admin",
    password: "********",
    database: "PostgreSQL",
    buildVersion: "2.1.0",
    buildingVersion: null,
    createdAt: "2023-11-20T14:45:00Z",
  },
  {
    id: "3",
    databaseName: "orders_db",
    username: "admin",
    password: "********",
    database: "Oracle",
    buildVersion: "1.5.0",
    buildingVersion: "1.6.0",
    createdAt: "2024-01-05T10:15:00Z",
  },
]

// 模拟表结构数据
const initialSchemas: TableSchema[] = [
  {
    id: "public",
    name: "public",
    tables: [
      {
        id: "sap_likp",
        name: "sap_likp",
        schemaId: "public"
      },
      {
        id: "sap_lips",
        name: "sap_lips",
        schemaId: "public"
      }
    ]
  },
  {
    id: "duckdb",
    name: "duckdb",
    tables: [
      {
        id: "duckdb_table1",
        name: "table1",
        schemaId: "duckdb"
      },
      {
        id: "duckdb_table2",
        name: "table2",
        schemaId: "duckdb"
      }
    ]
  },
  {
    id: "mooncake",
    name: "mooncake",
    tables: [
      {
        id: "mooncake_table1",
        name: "table1",
        schemaId: "mooncake"
      }
    ]
  },
  {
    id: "test",
    name: "test",
    tables: [
      {
        id: "test_table1",
        name: "table1",
        schemaId: "test"
      },
      {
        id: "test_table2",
        name: "table2",
        schemaId: "test"
      },
      {
        id: "test_table3",
        name: "table3",
        schemaId: "test"
      }
    ]
  }
]

// 模拟文档数据
interface Document {
  id: string
  name: string
  type: string
  description: string | null
  columnName: string
  columnType: string
  comment: string
  tableId: string
}

// 为每个表创建列数据
const initialDocuments: Document[] = [
  // sap_lips表的列
  {
    id: "sap_lips_1",
    name: "mandt",
    type: "character",
    description: null,
    columnName: "mandt",
    columnType: "character varying(3)",
    comment: "客户端",
    tableId: "sap_lips"
  },
  {
    id: "sap_lips_2",
    name: "vbeln",
    type: "character",
    description: "该字段可以关联到sap_likp表的vbeln字段获取数据",
    columnName: "vbeln",
    columnType: "character varying(10)",
    comment: "交货",
    tableId: "sap_lips"
  },
  {
    id: "sap_lips_3",
    name: "posnr",
    type: "numeric",
    description: null,
    columnName: "posnr",
    columnType: "numeric(6,0)",
    comment: "交货项目",
    tableId: "sap_lips"
  },
  {
    id: "sap_lips_4",
    name: "pstyv",
    type: "character",
    description: null,
    columnName: "pstyv",
    columnType: "character varying(4)",
    comment: "交货项目类别",
    tableId: "sap_lips"
  },
  {
    id: "sap_lips_5",
    name: "ernam",
    type: "character",
    description: null,
    columnName: "ernam",
    columnType: "character varying(12)",
    comment: "创建对象的人员名称",
    tableId: "sap_lips"
  },
  {
    id: "sap_lips_6",
    name: "erzet",
    type: "timestamp",
    description: null,
    columnName: "erzet",
    columnType: "timestamp without time zone",
    comment: "输入时间",
    tableId: "sap_lips"
  },
  {
    id: "sap_lips_7",
    name: "erdat",
    type: "date",
    description: "表示出货时间, 会与sap_likp表erdat字段存在在一一对应",
    columnName: "erdat",
    columnType: "date",
    comment: "记录的创建日期",
    tableId: "sap_lips"
  },

  // sap_likp表的列
  {
    id: "sap_likp_1",
    name: "mandt",
    type: "character",
    description: null,
    columnName: "mandt",
    columnType: "character varying(3)",
    comment: "客户端",
    tableId: "sap_likp"
  },
  {
    id: "sap_likp_2",
    name: "vbeln",
    type: "character",
    description: null,
    columnName: "vbeln",
    columnType: "character varying(10)",
    comment: "交货",
    tableId: "sap_likp"
  },
  {
    id: "sap_likp_3",
    name: "erdat",
    type: "date",
    description: null,
    columnName: "erdat",
    columnType: "date",
    comment: "创建日期",
    tableId: "sap_likp"
  }
]

export default function DatasetDetails({ params }: { params: { datasetId: string } }) {
  const router = useRouter()
  const [dataset, setDataset] = useState<Dataset | null>(null)
  const [schemas, setSchemas] = useState<TableSchema[]>(initialSchemas)
  const [documents, setDocuments] = useState<Document[]>(initialDocuments)
  const [searchTerm, setSearchTerm] = useState("")
  const [editingDocument, setEditingDocument] = useState<Document | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [documentDescription, setDocumentDescription] = useState("")

  // 当前选中的表和模式
  const [selectedTable, setSelectedTable] = useState<TableItem | null>(null)
  const [selectedSchema, setSelectedSchema] = useState<TableSchema | null>(null)

  // 获取数据集数据
  useEffect(() => {
    // 在实际应用中，这里应该从API获取数据集数据
    const ds = initialDatasets.find(ds => ds.id === params.datasetId)
    if (ds) {
      setDataset(ds)
    } else {
      // 如果找不到数据集，重定向到数据集列表页
      router.push("/datasets")
    }
  }, [params.datasetId, router])

  // 处理选择表格
  const handleSelectTable = (table: TableItem) => {
    setSelectedTable(table)
    const schema = schemas.find(s => s.id === table.schemaId) || null
    setSelectedSchema(schema)
    setSearchTerm("")
  }

  // 处理导航
  const handleNavigate = (type: "schema" | "table", id: string) => {
    if (type === "schema") {
      if (id === "schemas") {
        // 返回到所有模式
        setSelectedTable(null)
        setSelectedSchema(null)
      } else {
        // 导航到特定模式
        const schema = schemas.find(s => s.id === id) || null
        setSelectedSchema(schema)
        setSelectedTable(null)
      }
    }
  }

  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    // 如果选择了表格，只显示该表格的列
    if (selectedTable && doc.tableId !== selectedTable.id) {
      return false
    }

    // 搜索过滤
    return doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
           doc.comment.toLowerCase().includes(searchTerm.toLowerCase())
  })

  // 处理构建数据集
  const handleBuildDataset = () => {
    if (!dataset) return

    // 更新数据集的构建中版本
    const nextVersion = dataset.buildVersion
      ? incrementVersion(dataset.buildVersion)
      : "1.0.0"

    setDataset({
      ...dataset,
      buildingVersion: nextVersion
    })

    // 模拟构建过程
    setTimeout(() => {
      setDataset({
        ...dataset,
        buildVersion: nextVersion,
        buildingVersion: null
      })
    }, 3000)
  }

  // 版本号递增辅助函数
  const incrementVersion = (version: string): string => {
    const parts = version.split('.')
    if (parts.length !== 3) return "1.0.0"

    const patch = parseInt(parts[2] || "0", 10) + 1
    return `${parts[0]}.${parts[1]}.${patch}`
  }

  // 处理编辑文档
  const handleEditDocument = (document: Document) => {
    setEditingDocument(document)
    setDocumentDescription(document.description || "")
    setIsEditDialogOpen(true)
  }

  // 保存文档描述
  const handleSaveDescription = () => {
    if (!editingDocument) return

    setDocuments(documents.map(doc =>
      doc.id === editingDocument.id
        ? { ...doc, description: documentDescription }
        : doc
    ))

    setIsEditDialogOpen(false)
    setEditingDocument(null)
  }

  // 表格列定义
  const columns: ColumnDef<Document>[] = [
    {
      accessorKey: "columnName",
      header: "Column Name",
    },
    {
      accessorKey: "columnType",
      header: "Column Type",
    },
    {
      accessorKey: "comment",
      header: "Comment",
    },
    {
      accessorKey: "description",
      header: "Document",
      cell: ({ row }) => {
        const document = row.original
        return (
          <div className="flex items-center justify-between">
            <div className="relative group">
              <div className="truncate max-w-[200px]">
                {document.description || "-"}
              </div>
              {document.description && (
                <div className="absolute left-0 top-0 z-50 invisible group-hover:visible bg-popover text-popover-foreground rounded-md p-2 shadow-md text-sm max-w-[300px] whitespace-normal break-words">
                  {document.description}
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditDocument(document)}
            >
              <Pencil className="h-4 w-4" />
              <span className="sr-only">编辑</span>
            </Button>
          </div>
        )
      },
    },
  ]

  if (!dataset) {
    return <div>加载中...</div>
  }

  // 构建面包屑导航
  const breadcrumbs = [
    { title: "数据集列表", href: "/datasets", isCurrent: false },
    { title: dataset.databaseName, href: `/datasets/${params.datasetId}`, isCurrent: true }
  ]

  return (
    <CustomContainer
      title={`数据源 ${dataset.databaseName}`}
      breadcrumbs={breadcrumbs}
      action={
        <Button onClick={handleBuildDataset} disabled={!!dataset.buildingVersion}>
          {dataset.buildingVersion ? (
            <>
              <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
              构建中
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              构建
            </>
          )}
        </Button>
      }
    >
      {/* 数据集信息 */}
      <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6 p-4 bg-muted/30 rounded-lg">
        <div className="flex items-center gap-2">
          <span className="font-medium">创建时间:</span>
          {formatDate(new Date(dataset.createdAt))}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">数据库:</span>
          {dataset.database}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">数据构建版本:</span>
          {dataset.buildVersion || "无"}
        </div>
        {dataset.buildingVersion && (
          <div className="flex items-center gap-2">
            <span className="font-medium">构建中版本:</span>
            <Badge variant="outline">{dataset.buildingVersion}</Badge>
          </div>
        )}
      </div>



      {/* 面包屑导航 */}
      <TableBreadcrumb
        schema={selectedSchema}
        table={selectedTable}
        onNavigate={handleNavigate}
      />

      <div className="grid grid-cols-4 gap-6">
        {/* 左侧树状结构 */}
        <div className="col-span-1">
          <TableTree
            schemas={schemas}
            selectedTable={selectedTable}
            onSelectTable={handleSelectTable}
          />
        </div>

        {/* 右侧内容区域 */}
        <div className="col-span-3 space-y-4">
          {/* 搜索栏 */}
          <div className="flex items-center">
            <Search className="h-4 w-4 mr-2 text-muted-foreground" />
            <Input
              placeholder="搜索列名或注释..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {/* 表格标题 */}
          {selectedTable && (
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-medium">{selectedTable.name}</h2>
            </div>
          )}

          {/* 文档表格 */}
          <DataTable
            columns={columns}
            data={filteredDocuments}
          />

          {/* 无数据提示 */}
          {filteredDocuments.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              {selectedTable
                ? "该表格没有列数据"
                : "请从左侧选择一个表格查看列数据"}
            </div>
          )}
        </div>
      </div>

      {/* 编辑文档对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑文档</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h4 className="font-medium">列名: {editingDocument?.columnName}</h4>
              <p className="text-sm text-muted-foreground">
                {editingDocument?.comment}
              </p>
            </div>
            <Separator />
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                文档描述
              </label>
              <Textarea
                id="description"
                placeholder="输入文档描述..."
                value={documentDescription}
                onChange={(e) => setDocumentDescription(e.target.value)}
                rows={5}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveDescription}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </CustomContainer>
  )