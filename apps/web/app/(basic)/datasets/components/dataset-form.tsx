"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import { Alert, AlertDescription, AlertTitle } from "@ragtop-web/ui/components/alert"
import { type Dataset } from "../page"

// 表单验证模式
const formSchema = z.object({
  tablesetName: z.string().min(1,"数据集名称不能为空"),
  tablesetDescription: z.string().min(1, "数据集描述不能为空"),
  username: z.string().min(1, "用户名不能为空"),
  password: z.string().min(1, "密码不能为空"),
  databaseName: z.string().min(1, "数据库名称不能为空"),
})

type FormValues = z.infer<typeof formSchema>

interface DatasetFormProps {
  dataset?: Dataset
  onSave: (dataset: Dataset) => void
  isCreating: boolean
}

export function DatasetForm({ dataset, onSave, isCreating }: DatasetFormProps) {
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<"success" | "error" | null>(null)
  const [testMessage, setTestMessage] = useState("")

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: dataset
      ? { ...dataset }
      : {
        tablesetName:"",
        tablesetDescription: "",
        username: "",
        password: "",
        databaseName: "",
      },
  })

  // 测试连接
  const handleTestConnection = async () => {
    const values = form.getValues()
    const isValid = await form.trigger()

    if (!isValid) {
      return
    }

    setIsTesting(true)
    setTestResult(null)
    setTestMessage("")

    try {
      // 模拟API调用测试连接
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 随机模拟成功或失败 (实际应用中应该调用真实的API)
      const isSuccess = Math.random() > 0.3

      if (isSuccess) {
        setTestResult("success")
        setTestMessage("连接成功！数据库可以正常访问。")
      } else {
        setTestResult("error")
        setTestMessage("连接失败，请检查您的凭据和网络连接。")
      }
    } catch (error) {
      setTestResult("error")
      setTestMessage("发生错误，请稍后重试。")
    } finally {
      setIsTesting(false)
    }
  }

  // 保存数据集
  const handleSubmit = (values: FormValues) => {
    if (testResult !== "success") {
      setTestMessage("请先测试连接并确保连接成功")
      return
    }

    onSave({
      id: dataset?.id || "",
      ...values,
      buildVersion: dataset?.buildVersion || null,
      buildingVersion: dataset?.buildingVersion || null,
      createdAt: dataset?.createdAt || new Date().toISOString(),
    })
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="tablesetName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据集名称</FormLabel>
              <FormControl>
                <Input placeholder="输入数据集名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="tablesetDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据集描述</FormLabel>
              <FormControl>
                <Input placeholder="输入数据集描述" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="databaseName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据库</FormLabel>
              <FormControl>
                <Input placeholder="输入数据库" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据库用户名</FormLabel>
              <FormControl>
                <Input placeholder="输入用户名" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据库密码</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={isCreating ? "输入密码" : "••••••••"}
                  {...field}
                />
              </FormControl>
              <FormDescription>
                {!isCreating && "留空表示不修改密码"}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {testResult && (
          <Alert variant={testResult === "success" ? "default" : "destructive"}>
            {testResult === "success" ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertTitle>
              {testResult === "success" ? "连接成功" : "连接失败"}
            </AlertTitle>
            <AlertDescription>{testMessage}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleTestConnection}
            disabled={isTesting}
            className="flex-1"
          >
            {isTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            测试连接
          </Button>

          <Button
            type="submit"
            disabled={testResult !== "success"}
            className="flex-1"
          >
            保存
          </Button>
        </div>
      </form>
    </Form>
  )
}
