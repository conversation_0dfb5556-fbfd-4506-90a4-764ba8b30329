"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog"
import { Button } from "@ragtop-web/ui/components/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select"
import { SliceMethodDescription, sliceMethodOptions, type SliceMethodType } from "./slice-method-description"

// 表单验证模式
const formSchema = z.object({
  sliceMethod: z.string().min(1, "切片方法不能为空"),
})

type FormValues = z.infer<typeof formSchema>

interface ChunkMethodModalProps {
  open: boolean
  onClose: () => void
  onSubmit: (values: FormValues) => void
  initialMethod?: string
  fileId: string
  fileName: string
}

/**
 * 切片方法设置模态框
 */
export function ChunkMethodModal({
  open,
  onClose,
  onSubmit,
  initialMethod = "naive",
  fileId,
  fileName,
}: ChunkMethodModalProps) {
  const [selectedMethod, setSelectedMethod] = useState<SliceMethodType>(initialMethod as SliceMethodType)

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sliceMethod: initialMethod,
    },
  })

  // 监听选择的切片方法变化
  const watchSliceMethod = form.watch("sliceMethod") as SliceMethodType
  
  useEffect(() => {
    if (watchSliceMethod) {
      setSelectedMethod(watchSliceMethod)
    }
  }, [watchSliceMethod])

  // 处理表单提交
  const handleSubmit = (values: FormValues) => {
    onSubmit(values)
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置切片方法</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="text-sm text-muted-foreground mb-4">
              为文件 <span className="font-medium">{fileName}</span> 设置切片方法
            </div>

            <FormField
              control={form.control}
              name="sliceMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>切片方法</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="选择切片方法" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {sliceMethodOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    选择文档切片的方法
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 切片方法说明 */}
            <div className="border rounded-md p-3 bg-muted/30">
              <h4 className="font-medium mb-2">方法说明</h4>
              <SliceMethodDescription method={selectedMethod} />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button type="submit">确认</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
