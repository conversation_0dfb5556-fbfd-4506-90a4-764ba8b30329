"use client"

import { useState } from "react"
import { Trash2, Files } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card"
import { Button } from "@ragtop-web/ui/components/button"
import { type KnowledgeBase } from "../page"

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase
  onClick: () => void
  onDelete: () => void
  onManageFiles: (knowledgeBase: KnowledgeBase) => void
}

export function KnowledgeBaseCard({
  knowledgeBase,
  onClick,
  onDelete,
  onManageFiles
}: KnowledgeBaseCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete()
  }

  const handleManageFiles = (e: React.MouseEvent) => {
    e.stopPropagation()
    onManageFiles(knowledgeBase)
  }

  return (
    <Card
      className="cursor-pointer hover:border-primary/50 transition-colors relative"
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          {knowledgeBase.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium">文件数量:</span> {knowledgeBase.files.length} 个文件
          </p>
        </div>
      </CardContent>

      {/* 操作按钮 - 仅在悬停时显示 */}
      {isHovered && (
        <div className="absolute top-2 right-2 flex gap-1">
          {/* 文件管理按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-primary hover:text-primary hover:bg-primary/10"
            onClick={handleManageFiles}
          >
            <Files className="h-4 w-4" />
          </Button>

          {/* 删除按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
            onClick={handleDelete}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}
    </Card>
  )
}
