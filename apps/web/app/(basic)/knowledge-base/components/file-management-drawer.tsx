"use client"

import { useState } from "react"
import { 
  FileText, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  Play
} from "lucide-react"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { Button } from "@ragtop-web/ui/components/button"
import { ScrollArea } from "@ragtop-web/ui/components/scroll-area"
import { Checkbox } from "@ragtop-web/ui/components/checkbox"
import { Input } from "@ragtop-web/ui/components/input"
import { Search } from "lucide-react"
import { Badge } from "@ragtop-web/ui/components/badge"
import { Separator } from "@ragtop-web/ui/components/separator"
import { type KnowledgeBase } from "../page"

// 解析状态类型
export type ParseStatus = "success" | "failed" | "parsing" | "none"

// 扩展文件类型，添加解析状态
export type FileWithStatus = {
  id: string
  name: string
  type: string
  size: string
  parseStatus: ParseStatus
}

interface FileManagementDrawerProps {
  open: boolean
  onClose: () => void
  knowledgeBase: KnowledgeBase
  onParseFiles: (fileIds: string[]) => void
}

export function FileManagementDrawer({
  open,
  onClose,
  knowledgeBase,
  onParseFiles,
}: FileManagementDrawerProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([])
  
  // 过滤文件
  const filteredFiles = knowledgeBase.files.filter((file) =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 处理文件选择
  const handleFileSelect = (fileId: string) => {
    if (selectedFileIds.includes(fileId)) {
      setSelectedFileIds(selectedFileIds.filter((id) => id !== fileId))
    } else {
      setSelectedFileIds([...selectedFileIds, fileId])
    }
  }

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedFileIds.length === filteredFiles.length) {
      setSelectedFileIds([])
    } else {
      setSelectedFileIds(filteredFiles.map((file) => file.id))
    }
  }

  // 解析单个文件
  const handleParseFile = (fileId: string) => {
    onParseFiles([fileId])
  }

  // 批量解析文件
  const handleBatchParse = () => {
    if (selectedFileIds.length > 0) {
      onParseFiles(selectedFileIds)
      setSelectedFileIds([])
    }
  }

  // 渲染解析状态图标
  const renderStatusIcon = (status: ParseStatus) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "parsing":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "none":
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  // 获取解析状态文本
  const getStatusText = (status: ParseStatus) => {
    switch (status) {
      case "success":
        return "解析成功"
      case "failed":
        return "解析失败"
      case "parsing":
        return "解析中"
      case "none":
      default:
        return "未解析"
    }
  }

  // 获取解析按钮文本
  const getParseButtonText = (status: ParseStatus) => {
    return status !== "none" ? "重新解析" : "解析"
  }

  return (
    <CustomDrawer
      open={open}
      onClose={onClose}
      title={`${knowledgeBase.name} - 文件管理`}
    >
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">文件列表</h3>
          </div>
          <Badge variant="outline" className="ml-2">
            {knowledgeBase.files.length} 个文件
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索文件..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {selectedFileIds.length > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              已选择 {selectedFileIds.length} 个文件
            </span>
            <Button
              size="sm"
              variant="default"
              onClick={handleBatchParse}
              className="flex items-center gap-1"
            >
              <Play className="h-3.5 w-3.5" />
              批量解析
            </Button>
          </div>
        )}

        <Separator />

        <ScrollArea className="h-[400px]">
          <div className="space-y-1">
            <div className="flex items-center p-2 bg-muted/50 rounded-md">
              <Checkbox
                id="select-all"
                checked={
                  filteredFiles.length > 0 &&
                  selectedFileIds.length === filteredFiles.length
                }
                onCheckedChange={handleSelectAll}
                className="mr-2"
              />
              <div className="flex-1 grid grid-cols-12 text-sm font-medium">
                <span className="col-span-6">文件名称</span>
                <span className="col-span-3">解析状态</span>
                <span className="col-span-3 text-right">操作</span>
              </div>
            </div>

            {filteredFiles.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                未找到匹配的文件
              </div>
            ) : (
              filteredFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center p-2 hover:bg-muted rounded-md"
                >
                  <Checkbox
                    id={`file-${file.id}`}
                    checked={selectedFileIds.includes(file.id)}
                    onCheckedChange={() => handleFileSelect(file.id)}
                    className="mr-2"
                  />
                  <div className="flex-1 grid grid-cols-12 items-center text-sm">
                    <div className="col-span-6 flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-primary" />
                      <span className="truncate">{file.name}</span>
                    </div>
                    <div className="col-span-3 flex items-center">
                      {renderStatusIcon(file.parseStatus)}
                      <span className="ml-1 text-xs">
                        {getStatusText(file.parseStatus)}
                      </span>
                    </div>
                    <div className="col-span-3 flex justify-end">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 text-xs flex items-center gap-1"
                        onClick={() => handleParseFile(file.id)}
                      >
                        {file.parseStatus !== "none" ? (
                          <RefreshCw className="h-3 w-3" />
                        ) : (
                          <Play className="h-3 w-3" />
                        )}
                        {getParseButtonText(file.parseStatus)}
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </CustomDrawer>
  )
}
