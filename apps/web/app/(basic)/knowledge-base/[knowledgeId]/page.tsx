"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import {
  Search,
  FileText,
  PlusIcon,
  Trash2,
  Settings,
  Play,
  Square,
  RefreshCw,
  CheckCircle2,
  XCircle
} from "lucide-react"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import { Input } from "@ragtop-web/ui/components/input"
import { Button } from "@ragtop-web/ui/components/button"
import { Badge } from "@ragtop-web/ui/components/badge"
import { Checkbox } from "@ragtop-web/ui/components/checkbox"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { Switch } from "@ragtop-web/ui/components/switch"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ragtop-web/ui/components/alert-dialog"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@ragtop-web/ui/components/tooltip"
import { type KnowledgeBase, type ParseStatus } from "../page"
import { formatDate } from "@/lib/utils"
import type { ReactElement } from "react"
import { ChunkMethodModal } from "../components/chunk-method-modal"
import { sliceMethodOptions } from "../components/slice-method-description"

// 模拟知识库数据
const initialKnowledgeBases: KnowledgeBase[] = [
  {
    id: "1",
    name: "产品文档",
    avatar: "/images/kb-product.png",
    parser: "Deepdoc",
    sliceMethod: "naive",
    files: [
      { id: "1", name: "产品说明书.pdf", type: "pdf", size: "2.4 MB", parseStatus: "success", enabled: true },
      { id: "3", name: "技术文档.pdf", type: "pdf", size: "1.8 MB", parseStatus: "failed", enabled: true },
    ],
  },
  {
    id: "2",
    name: "技术手册",
    avatar: "/images/kb-tech.png",
    parser: "MinerU",
    sliceMethod: "qa",
    files: [
      { id: "4", name: "API文档.pdf", type: "pdf", size: "4.2 MB", parseStatus: "parsing", enabled: true },
      { id: "7", name: "系统架构.pdf", type: "pdf", size: "2.7 MB", parseStatus: "none", enabled: false },
    ],
  },
  {
    id: "3",
    name: "用户指南",
    avatar: "/images/kb-user.png",
    parser: "Deepdoc",
    sliceMethod: "paper",
    files: [
      { id: "2", name: "用户手册.pdf", type: "pdf", size: "3.1 MB", parseStatus: "success", enabled: true },
      { id: "6", name: "常见问题.pdf", type: "pdf", size: "0.9 MB", parseStatus: "none", enabled: true },
    ],
  },
]

// 渲染状态图标
function renderStatusBadge(status: string): ReactElement {
  switch (status) {
    case "success":
      return <Badge variant="default" className="bg-green-50 text-green-700 border-green-200">解析成功</Badge>
    case "failed":
      return <Badge variant="destructive">解析失败</Badge>
    case "parsing":
      return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">解析中</Badge>
    case "none":
      return <Badge variant="outline">未解析</Badge>
    default:
      return <Badge variant="outline">未知状态</Badge>
  }
}

export default function KnowledgeBaseDetails({ params }: { params: { knowledgeId: string } }) {
  const router = useRouter()
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([])

  // 切片方法设置模态框状态
  const [isChunkMethodModalOpen, setIsChunkMethodModalOpen] = useState(false)
  const [selectedFileForChunkMethod, setSelectedFileForChunkMethod] = useState<{id: string, name: string} | null>(null)

  // 删除确认对话框状态
  const [fileToDelete, setFileToDelete] = useState<{id: string, name: string} | null>(null)

  // 获取知识库数据
  useEffect(() => {
    // 在实际应用中，这里应该从API获取知识库数据
    const kb = initialKnowledgeBases.find(kb => kb.id === params.knowledgeId)
    if (kb) {
      setKnowledgeBase(kb)
    } else {
      // 如果找不到知识库，重定向到知识库列表页
      router.push("/knowledge-base")
    }
  }, [params.knowledgeId, router])

  // 过滤文件
  const filteredFiles = knowledgeBase?.files.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  // 处理文件选择
  const handleFileSelect = (fileId: string) => {
    if (selectedFileIds.includes(fileId)) {
      setSelectedFileIds(selectedFileIds.filter(id => id !== fileId))
    } else {
      setSelectedFileIds([...selectedFileIds, fileId])
    }
  }

  // 处理全选
  const handleSelectAll = () => {
    if (filteredFiles.length > 0 && selectedFileIds.length === filteredFiles.length) {
      setSelectedFileIds([])
    } else {
      setSelectedFileIds(filteredFiles.map(file => file.id))
    }
  }

  // 处理添加新文档
  const handleAddNewDocument = () => {
    console.log("添加新文档")
    // 这里可以添加打开文件选择抽屉的逻辑
  }

  // 处理解析文件
  const handleParseFile = (fileId: string, currentStatus: ParseStatus) => {
    if (!knowledgeBase) return

    // 根据当前状态决定操作
    const newStatus: ParseStatus = currentStatus === "parsing" ? "none" : "parsing"
    const actionText = currentStatus === "parsing" ? "停止解析" : (currentStatus === "failed" ? "重试解析" : "解析")

    console.log(`${actionText}文件`, fileId)

    // 更新文件状态
    const updatedFiles = knowledgeBase.files.map(file => {
      if (file.id === fileId) {
        return { ...file, parseStatus: newStatus }
      }
      return file
    })

    setKnowledgeBase({
      ...knowledgeBase,
      files: updatedFiles
    })

    // 如果是开始解析，模拟解析过程
    if (newStatus === "parsing") {
      setTimeout(() => {
        const result: ParseStatus = Math.random() > 0.3 ? "success" : "failed"
        const finalFiles = knowledgeBase.files.map(file => {
          if (file.id === fileId) {
            return { ...file, parseStatus: result }
          }
          return file
        })

        setKnowledgeBase({
          ...knowledgeBase,
          files: finalFiles
        })
      }, 2000)
    }
  }

  // 处理切换文件启用状态
  const handleToggleEnabled = (fileId: string) => {
    if (!knowledgeBase) return

    const updatedFiles = knowledgeBase.files.map(file => {
      if (file.id === fileId) {
        return { ...file, enabled: !file.enabled }
      }
      return file
    })

    setKnowledgeBase({
      ...knowledgeBase,
      files: updatedFiles
    })
  }

  // 处理设置切片方法
  const handleSetChunkMethod = (fileId: string, fileName: string) => {
    setSelectedFileForChunkMethod({ id: fileId, name: fileName })
    setIsChunkMethodModalOpen(true)
  }

  // 处理保存切片方法
  const handleSaveChunkMethod = (values: { sliceMethod: string }) => {
    console.log("保存切片方法", selectedFileForChunkMethod?.id, values.sliceMethod)
    // 实际应用中这里应该调用API保存切片方法
  }

  // 处理删除文件
  const handleDeleteFile = (fileId: string, fileName: string) => {
    setFileToDelete({ id: fileId, name: fileName })
  }

  // 确认删除文件
  const confirmDeleteFile = () => {
    if (!fileToDelete || !knowledgeBase) return

    const updatedFiles = knowledgeBase.files.filter(file => file.id !== fileToDelete.id)

    setKnowledgeBase({
      ...knowledgeBase,
      files: updatedFiles
    })

    setFileToDelete(null)
  }

  if (!knowledgeBase) {
    return (
      <CustomContainer title="知识库详情">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </CustomContainer>
    )
  }

  // 构建面包屑导航
  const breadcrumbs = [
    { title: "知识库列表", href: "/knowledge-base", isCurrent: false },
    { title: knowledgeBase.name, href: `/knowledge-base/${params.knowledgeId}`, isCurrent: true }
  ]

  return (
    <CustomContainer
      title={knowledgeBase.name}
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-6">
        {/* 知识库信息 */}
        <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6 p-4 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="font-medium">创建时间:</span>
            {formatDate(new Date())}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">解析器:</span>
            {knowledgeBase.parser}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">文档数量:</span>
            {knowledgeBase.files.length}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">切片方法:</span>
            {knowledgeBase.sliceMethod}
          </div>
        </div>

        {/* 搜索和操作栏 */}
        <div className="flex items-center justify-between">
          <div className="relative w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索文件..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button onClick={handleAddNewDocument}>
            <PlusIcon className="h-4 w-4 mr-2" />
            新增文档
          </Button>
        </div>

        {/* 文件列表 */}
        {(() => {
          // 定义表格列
          const columns: ColumnDef<typeof knowledgeBase.files[0]>[] = [
            {
              id: "select",
              header: ({ table }) => (
                <Checkbox
                  checked={
                    filteredFiles.length > 0 &&
                    selectedFileIds.length === filteredFiles.length
                  }
                  onCheckedChange={handleSelectAll}
                />
              ),
              cell: ({ row }) => (
                <Checkbox
                  checked={selectedFileIds.includes(row.original.id)}
                  onCheckedChange={() => handleFileSelect(row.original.id)}
                />
              ),
              size: 40,
            },
            {
              accessorKey: "name",
              header: "名称",
              cell: ({ row }) => (
                <div className="font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4 text-primary" />
                  {row.original.name}
                </div>
              ),
            },
            {
              accessorKey: "segments",
              header: "分段数",
              cell: () => 0,
            },
            {
              accessorKey: "uploadDate",
              header: "上传日期",
              cell: () => formatDate(new Date()),
            },
            {
              accessorKey: "sliceMethod",
              header: "切片方法",
              cell: () => {
                const method = knowledgeBase.sliceMethod;
                const option = sliceMethodOptions.find(opt => opt.value === method);
                return option ? option.label : method;
              },
            },
            {
              accessorKey: "enabled",
              header: "启用",
              cell: ({ row }) => {
                const file = row.original;
                return (
                  <Switch
                    checked={file.enabled}
                    onCheckedChange={() => handleToggleEnabled(file.id)}
                  />
                );
              },
            },
            {
              accessorKey: "parseStatus",
              header: "状态",
              cell: ({ row }) => renderStatusBadge(row.original.parseStatus),
            },
            {
              id: "actions",
              header: () => <div className="text-right">操作</div>,
              cell: ({ row }) => {
                const file = row.original;
                return (
                  <div className="flex items-center justify-end gap-1">
                    <TooltipProvider>
                      {/* 解析按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleParseFile(file.id, file.parseStatus)}
                          >
                            {file.parseStatus === "parsing" ? (
                              <Square className="h-4 w-4 text-amber-500" />
                            ) : file.parseStatus === "failed" ? (
                              <RefreshCw className="h-4 w-4 text-destructive" />
                            ) : (
                              <Play className="h-4 w-4 text-primary" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {file.parseStatus === "parsing"
                            ? "停止解析"
                            : file.parseStatus === "failed"
                              ? "重试解析"
                              : "解析文件"}
                        </TooltipContent>
                      </Tooltip>

                      {/* 切片方法设置按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleSetChunkMethod(file.id, file.name)}
                          >
                            <Settings className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          设置切片方法
                        </TooltipContent>
                      </Tooltip>

                      {/* 删除按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleDeleteFile(file.id, file.name)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          删除文件
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                );
              },
            },
          ];

          return <DataTable columns={columns} data={filteredFiles} />;
        })()}

        {/* 分页 */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            共计 {filteredFiles.length} 个文件
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </div>
      </div>

      {/* 切片方法设置模态框 */}
      {selectedFileForChunkMethod && (
        <ChunkMethodModal
          open={isChunkMethodModalOpen}
          onClose={() => setIsChunkMethodModalOpen(false)}
          onSubmit={handleSaveChunkMethod}
          initialMethod={knowledgeBase.sliceMethod}
          fileId={selectedFileForChunkMethod.id}
          fileName={selectedFileForChunkMethod.name}
        />
      )}

      {/* 删除确认对话框 */}
      <AlertDialog
        open={!!fileToDelete}
        onOpenChange={(open) => !open && setFileToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除文件</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除文件 <span className="font-medium">{fileToDelete?.name}</span> 吗？
              此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteFile}
              className="bg-destructive text-destructive-foreground"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </CustomContainer>
  )
}