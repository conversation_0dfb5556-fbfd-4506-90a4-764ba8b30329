"use client"

import { useEffect, useState } from "react"
import { ChatContainer, type Message } from "@/components/chat/chat-container"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"

// 模拟Agent数据
const mockAgents: Record<string, { name: string, description: string }> = {
  "agentaaa": { name: "智能助手Alpha", description: "通用AI助手" },
  "agentbbb": { name: "数据分析师Beta", description: "专注数据分析的助手" },
  // 可以添加更多模拟数据
}

// 模拟会话数据
const mockSessions: Record<string, { name: string, createdAt: string }> = {
  "sessionaaa": { name: "历史会话", createdAt: "2023-05-15T10:30:00Z" },
  "sessionbbb": { name: "收藏会话", createdAt: "2023-06-20T14:45:00Z" },
  "sessionccc": { name: "设置会话", createdAt: "2023-07-10T09:15:00Z" },
  // 可以添加更多模拟数据
}

export default function SessionPage({ params }: { params: { agentId: string, sessionId: string } }) {
  const [agentName, setAgentName] = useState<string>("")
  const [sessionName, setSessionName] = useState<string>("")

  // 获取Agent和Session信息
  useEffect(() => {
    // 在实际应用中，这里应该从API获取数据
    const agent = mockAgents[params.agentId]
    const session = mockSessions[params.sessionId]

    if (agent) {
      setAgentName(agent.name)
    }

    if (session) {
      setSessionName(session.name)
    }
  }, [params.agentId, params.sessionId])

  // 模拟初始消息
  const initialMessages: Message[] = [
    {
      id: "assistant-1",
      role: "assistant",
      content: "你好！我是你的助手，有什么可以帮到你的吗？",
      timestamp: new Date().toISOString()
    },
    {
      id: "user-1",
      role: "user",
      content: "hello",
      timestamp: new Date().toISOString()
    },
    {
      id: "assistant-2",
      role: "assistant",
      content: "你好！有什么我可以帮助你的吗？",
      timestamp: new Date().toISOString()
    }
  ]

  // 构建聊天标题
  const chatTitle = agentName && sessionName
    ? `${agentName} - ${sessionName}`
    : "Ragtop 聊天"

  // 构建面包屑导航
  const breadcrumbs = [
    { title: "Agent列表", href: "/", isCurrent: false },
    { title: agentName, href: `/agent/${params.agentId}`, isCurrent: false },
    { title: sessionName, href: `/agent/${params.agentId}/${params.sessionId}`, isCurrent: true }
  ]

  return (
    <CustomContainer
      title={chatTitle}
      breadcrumbs={breadcrumbs}
      className="max-w-4xl mx-auto h-[calc(100vh-8rem)] py-4"
    >
      <ChatContainer
        initialMessages={initialMessages}
        agentName={agentName}
      />
    </CustomContainer>
  )
}