"use client"

import { AgentDetails } from "@/components/agent/agent-details"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { Flexibility } from "@/service/agent-service"

// 模拟Agent数据
const mockAgent = {
  name: "测试助理",
  description: "这是一个测试助理，用于展示Agent详情页面",
  avatar: null,
  emptyReply: "抱歉，我无法回答这个问题。",
  hello_message: "你好！我是你的助手，有什么可以帮到你的吗？",
  showCitation: true,
  refineMultiturn: false,
  reasoning: false,
  resourceType: "knowledgeBase" as "knowledgeBase", // 资源类型默认为"知识库"
  resource: "kb1", // 选择的资源ID
  agentPermission: "personal" as "personal", // Agent权限默认为"个人"
  systemPrompt: "你是一个学术领域的专家，请根据知识库的内容来尽可能详细的回答问题。\n以下是知识库：\n{knowledge}\n以上是知识库。",
  similarityThreshold: 0.5,
  keywordWeight: 0.5,
  topN: 5,
  reRankModel: "none",
  keyword: [],
  model: "gpt-4",
  temperature: 0.1,
  topP: 0.3,
  presencePenalty: 0.4,
  frequencyPenalty: 0.7,
  freedom: Flexibility.Deliberate
}

export default function AgentPage({ params }: { params: { agentId: string } }) {
  // 在实际应用中，这里应该根据agentId从API获取Agent数据
  console.log(`加载Agent ID: ${params.agentId}`)
  const agent = mockAgent

  // 处理更新Agent
  const handleUpdateAgent = (data: any) => {
    console.log("更新Agent:", data)
    // 这里可以添加实际的Agent更新逻辑
  }

  // 构建面包屑导航
  const breadcrumbs = [
    { title: "Agent列表", href: "/", isCurrent: false },
    { title: agent.name, href: `/agent/${params.agentId}`, isCurrent: true }
  ]

  return (
    <CustomContainer
      title={agent.name}
      breadcrumbs={breadcrumbs}
      className="py-8"
    >
      <AgentDetails agent={agent} onUpdate={handleUpdateAgent} />
    </CustomContainer>
  )
}