"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON>, UserRound } from "lucide-react"
import { Button } from "@ragtop-web/ui/components/button"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import { Badge } from "@ragtop-web/ui/components/badge"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { Pagination } from "@ragtop-web/ui/components/pagination"
import { toast } from "sonner"
import { AddMembersDialog } from "./components/add-members-dialog"
import { DeleteMemberDialog } from "./components/delete-member-dialog"
import { useMembers, useMemberDelete, useMemberCreate, UserList } from "@/service/team-service"
import { useIsTeamAdmin } from "@/lib/user-role"

// 用户数据结构（用于表格展示）
interface TeamMember {
  id: string
  name: string
  role: "admin" | "member"
}

export default function UsersPage() {
  // 分页状态
  const [pageN<PERSON><PERSON>, setPageNumber] = useState(1)
  const pageSize = 10 // 固定每页显示数量

  // 对话框状态
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [memberToDelete, setMemberToDelete] = useState<TeamMember | null>(null)

  // API hooks
  const { data, isLoading } = useMembers(pageNumber, pageSize)
  const memberDeleteMutation = useMemberDelete()
  const memberCreateMutation = useMemberCreate()

    // 从响应中提取用户列表和总数
    const fmtMember = (records: UserList[]) => {
        const formattedMembers = records.map((member: UserList) => {
          const role = member.roles?.includes("TEAM_ADMIN") ? "admin" as const : "member" as const
          return {
            id: member.id,
            name: member.user.nick || member.user.username,
            role: role
          }
        })
        return formattedMembers
      }
  const members = fmtMember(data?.records || [])
  const totalItems = data?.total || 0

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPageNumber(page)
  }

  // 处理打开添加成员对话框
  const handleOpenAddDialog = () => {
    setIsAddDialogOpen(true)
  }

  // 处理关闭添加成员对话框
  const handleCloseAddDialog = () => {
    setIsAddDialogOpen(false)
  }

  // 处理添加成员
  const handleAddMembers = async (user_id: string ) => {
    try {
      // 批量添加成员
        await memberCreateMutation.mutateAsync({
          user_id
        })

      toast.success("成员添加成功")
      setIsAddDialogOpen(false)
    } catch (error) {
      console.error("添加成员失败:", error)
      toast.error("添加成员失败")
    }
  }

  // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (member: TeamMember) => {
    setMemberToDelete(member)
    setIsDeleteDialogOpen(true)
  }

  // 处理关闭删除确认对话框
  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false)
    setMemberToDelete(null)
  }

  // 处理删除成员
  const handleDeleteMember = async () => {
    if (memberToDelete) {
      try {
        await memberDeleteMutation.mutateAsync({
          member_id: memberToDelete.id
        })

        toast.success("成员删除成功")
      } catch (error) {
        console.error("删除成员失败:", error)
        toast.error("删除成员失败")
      }
    }
    setIsDeleteDialogOpen(false)
    setMemberToDelete(null)
  }

  // 定义表格列
  const columns: ColumnDef<TeamMember>[] = [
    {
      accessorKey: "name",
      header: "账户名",
      cell: ({ row }) => (
        <div className="font-medium flex items-center gap-2">
          <UserRound className="h-4 w-4 text-muted-foreground" />
          {row.original.name}
        </div>
      ),
    },
    {
      accessorKey: "role",
      header: "角色",
      cell: ({ row }) => {
        const role = row.original.role;
        return role === "admin" ? (
          <Badge className="flex items-center gap-1 w-fit">
            <ShieldCheck className="h-3 w-3" />
            管理员
          </Badge>
        ) : (
          <Badge variant="secondary" className="w-fit">
            成员
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="w-[100px]">操作</div>,
      cell: ({ row }) => {
        const member = row.original;

        // 只有管理员才能删除成员，且不能删除管理员
        return isAdmin && member.role !== "admin" ? (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleOpenDeleteDialog(member)}
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">删除</span>
            </Button>
        ) : null;
      },
    },
  ];

  // 检查用户是否是管理员
  const isAdmin = useIsTeamAdmin()

  return (
    <CustomContainer
     title="团队成员"
     action={isAdmin ? (
       <Button onClick={handleOpenAddDialog}>
         <PlusIcon className="h-4 w-4" />
         添加成员
       </Button>
     ) : undefined}
   >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
            <p className="text-sm text-muted-foreground">加载中...</p>
          </div>
        </div>
      ) : (
        <>
          <DataTable columns={columns} data={members} />
          {totalItems > pageSize && (
            <div className="flex justify-center mt-4">
              <Pagination
                pageCount={Math.ceil(totalItems / pageSize)}
                currentPage={pageNumber}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}

      {/* 添加成员对话框 */}
      <AddMembersDialog
        open={isAddDialogOpen}
        onClose={handleCloseAddDialog}
        onAddMembers={handleAddMembers}
      />

      {/* 删除成员确认对话框 */}
      <DeleteMemberDialog
        open={isDeleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onDelete={handleDeleteMember}
        memberName={memberToDelete?.name || ""}
      />
    </CustomContainer>
  )
}