"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { Search, Loader2 } from "lucide-react"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import { Label } from "@ragtop-web/ui/components/label"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { toast } from "sonner"
import { useAvailableMembers } from "@/service/auth-service"

// 分页响应接口
interface PaginatedResponse<T> {
  page_number: number
  page_size: number
  total: number
  records: T[]
}

// 可添加的用户数据结构
interface AvailableUser {
  user_id: string
  name: string
}

// API响应中的用户数据结构
interface ApiUser {
  id: string
  user: {
    id: string
    nick?: string
    username?: string
  }
}

interface AddMembersDialogProps {
  open: boolean
  onClose: () => void
  onAddMembers: (user_id: string ) => void
}

// 防抖函数
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  const previousValueRef = useRef<T>(value)

  useEffect(() => {
    // 如果值没有变化，不触发防抖
    if (value === previousValueRef.current) {
      return
    }

    previousValueRef.current = value

    const timer = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(timer)
    }
  }, [value, delay])

  return debouncedValue
}

export function AddMembersDialog({
  open,
  onClose,
  onAddMembers,
}: AddMembersDialogProps) {
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [searchQuery, setSearchQuery] = useState("")
  const [availableUsers, setAvailableUsers] = useState<AvailableUser[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [pageNumber, setPageNumber] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // 使用防抖处理搜索查询
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // API hooks
  const availableMembersQuery = useAvailableMembers()

  // 请求状态引用 - 用于防止重复请求
  const requestRef = useRef<{
    isRequesting: boolean;
    lastKeyword: string;
    lastPage: number;
  }>({
    isRequesting: false,
    lastKeyword: "",
    lastPage: 0
  })

  // 获取可添加的成员列表
  const fetchAvailableUsers = useCallback(async (page: number, keyword: string, reset: boolean = false) => {
    // 如果已经在请求中，直接返回
    if (requestRef.current.isRequesting) {
      return
    }

    // 如果是相同的请求参数且不是强制刷新，直接返回
    if (!reset &&
        page === requestRef.current.lastPage &&
        keyword === requestRef.current.lastKeyword) {
      return
    }

    // 标记请求状态
    requestRef.current.isRequesting = true

    // 设置加载状态
    if (page === 1) {
      // 如果是第一页，显示全屏加载
      setIsLoading(true)
    }

    try {
      // 发起请求，直接传递keyword参数到接口
      const response = await availableMembersQuery.mutateAsync({
        page_number: page,
        page_size: 10,
        keyword: keyword || undefined
      })

      // 更新请求参数记录
      requestRef.current.lastPage = page
      requestRef.current.lastKeyword = keyword

      // 转换数据格式
      const paginatedResponse = response as PaginatedResponse<ApiUser>
      if (paginatedResponse && paginatedResponse.records && Array.isArray(paginatedResponse.records)) {
        const formattedUsers = paginatedResponse.records.map((item) => ({
          user_id: item.user?.id || "",
          name: item.user?.nick || item.user?.username || "未知用户"
        }))

        // 更新用户列表
        if (reset) {
          setAvailableUsers(formattedUsers)
        } else {
          setAvailableUsers(prev => [...prev, ...formattedUsers])
        }

        // 判断是否还有更多数据
        const total = paginatedResponse.total || 0
        const currentCount = (page * 10)
        setHasMore(currentCount < total)
      } else {
        // 处理空响应
        if (reset) {
          setAvailableUsers([])
        }
        setHasMore(false)
      }
    } catch (error) {
      console.error("获取可添加成员失败:", error)
      toast.error("获取可添加成员失败")
    } finally {
      // 重置请求状态
      setIsLoading(false)
      requestRef.current.isRequesting = false
    }
  }, [availableMembersQuery])

  // 当对话框打开时获取数据
  useEffect(() => {
    if (open) {
      // 重置状态并获取第一页数据
      setPageNumber(1)
      fetchAvailableUsers(1, "", true)
    }

    // 清理函数 - 当组件卸载或依赖项变化时执行
    return () => {
      if (!open) {
        // 如果对话框关闭，重置所有状态
        requestRef.current = {
          isRequesting: false,
          lastKeyword: "",
          lastPage: 0
        }
      }
    }
  }, [open])

  // 当搜索查询变化时重新获取数据
  useEffect(() => {
    // 只有当对话框打开且防抖后的搜索词发生变化时才触发
    if (open && debouncedSearchQuery !== requestRef.current.lastKeyword) {
      setPageNumber(1)
      fetchAvailableUsers(1, debouncedSearchQuery, true)
    }
  }, [debouncedSearchQuery, open, fetchAvailableUsers])

  // 处理加载更多
  const handleLoadMore = () => {
    const nextPage = pageNumber + 1
    setPageNumber(nextPage)
    fetchAvailableUsers(nextPage, debouncedSearchQuery, false)
  }

  // 处理滚动加载
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
    if (scrollHeight - scrollTop <= clientHeight * 1.5 && !isLoading && hasMore) {
      handleLoadMore()
    }
  }

  // 处理选择用户
  const handleSelectUser = (userId: string) => {
    setSelectedUserId(userId)
  }

  // 处理添加成员
  const handleAddMembers = () => {
    onAddMembers(selectedUserId)
    setSelectedUserId("")
  }

  // 处理关闭对话框
  const handleClose = () => {
    // 重置所有状态
    setSelectedUserId("")
    setSearchQuery("")
    setAvailableUsers([])
    setPageNumber(1)
    setHasMore(true)

    // 重置请求状态引用
    requestRef.current = {
      isRequesting: false,
      lastKeyword: "",
      lastPage: 0
    }

    // 调用父组件的关闭回调
    onClose()
  }

  return (
    <CustomDrawer open={open} onClose={handleClose} title="添加团队成员" footer={
      <Button
        onClick={handleAddMembers}
        disabled={selectedUserId === ""}
      >
        添加成员
      </Button>
    }>
      <span>从现有账户中选择要添加到团队的成员</span>

      <div className="py-4 space-y-4">
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索成员..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* 用户列表 */}
        <div className="border rounded-md">
          <div
            className="max-h-[300px] overflow-y-auto p-2 space-y-2"
            onScroll={handleScroll}
          >
            {isLoading && pageNumber === 1 ? (
              <div className="py-6 text-center">
                <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">加载中...</p>
              </div>
            ) : availableUsers.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                未找到匹配的成员
              </div>
            ) : (
              <>
                {availableUsers.map((user) => (
                  <div
                    key={user.user_id}
                    className={`flex items-center space-x-2 rounded-md p-2 hover:bg-accent cursor-pointer ${selectedUserId === user.user_id ? 'bg-accent' : ''}`}
                    onClick={() => handleSelectUser(user.user_id)}
                  >
                    <div className={`size-4 rounded-full border ${selectedUserId === user.user_id ? 'bg-primary border-primary' : 'border-input'}`}>
                      {selectedUserId === user.user_id && (
                        <div className="size-2 rounded-full bg-white m-[3px]" />
                      )}
                    </div>
                    <Label
                      className="flex flex-row cursor-pointer flex-1"
                    >
                      <span>{user.name}</span>
                    </Label>
                  </div>
                ))}

                {isLoading && pageNumber > 1 && (
                  <div className="py-2 text-center">
                    <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                  </div>
                )}

                {!isLoading && !hasMore && availableUsers.length > 0 && (
                  <div className="py-2 text-center text-sm text-muted-foreground">
                    已到达列表底部
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* 已选择提示 */}
        {selectedUserId !== "" && (
          <div className="text-sm text-muted-foreground">
            已选择 1 名成员
          </div>
        )}
      </div>
    </CustomDrawer>
  )
}
