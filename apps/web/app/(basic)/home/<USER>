"use client"

import { useEffect, useState } from "react"
import { Bot, Database, BookOpenText, FileText } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ragtop-web/ui/components/card"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useRouter } from "next/navigation"
import { useIsTeamAdmin } from "@/lib/user-role"

/**
 * 首页组件
 * 
 * 显示系统概览和快速访问链接
 */
export default function HomePage() {
  const router = useRouter()
  const isAdmin = useIsTeamAdmin()
  
  // 快速访问卡片数据
  const quickAccessCards = [
    {
      title: "知识库",
      description: "管理和访问您的知识库资源",
      icon: <BookOpenText className="h-10 w-10 text-primary" />,
      url: "/knowledge-base",
    },
    {
      title: "数据集",
      description: "浏览和管理您的数据集",
      icon: <Database className="h-10 w-10 text-primary" />,
      url: "/datasets",
    },
    {
      title: "文件",
      description: "上传和管理您的文件",
      icon: <FileText className="h-10 w-10 text-primary" />,
      url: "/files",
    },
    {
      title: "Agent",
      description: "创建和管理您的智能助手",
      icon: <Bot className="h-10 w-10 text-primary" />,
      url: "#",
      action: () => document.querySelector<HTMLButtonElement>('button:has(.lucide-plus)')?.click(),
    },
  ]
  
  // 处理卡片点击
  const handleCardClick = (card: typeof quickAccessCards[0]) => {
    if (card.action) {
      card.action()
    } else if (card.url) {
      router.push(card.url)
    }
  }
  
  return (
    <CustomContainer title="欢迎使用 Ragtop">
      <div className="space-y-8">
        {/* 欢迎信息 */}
        <div className="bg-muted/30 p-6 rounded-lg">
          <h2 className="text-2xl font-bold mb-2">欢迎回来</h2>
          <p className="text-muted-foreground">
            Ragtop 是一个强大的知识库和数据集管理平台，帮助您更高效地管理和利用数据资源。
            {isAdmin ? " 作为团队管理员，您可以管理团队成员和配置模型。" : " 您可以访问团队共享的资源和创建个人资源。"}
          </p>
        </div>
        
        {/* 快速访问卡片 */}
        <div>
          <h3 className="text-lg font-medium mb-4">快速访问</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickAccessCards.map((card, index) => (
              <Card 
                key={index} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleCardClick(card)}
              >
                <CardHeader className="pb-2">
                  <div className="mb-2">{card.icon}</div>
                  <CardTitle>{card.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{card.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        
        {/* 最近活动 */}
        <div>
          <h3 className="text-lg font-medium mb-4">最近活动</h3>
          <div className="bg-card border rounded-lg p-4">
            <p className="text-center text-muted-foreground py-8">
              暂无最近活动记录
            </p>
          </div>
        </div>
        
        {/* 管理员专属内容 */}
        {isAdmin && (
          <div>
            <h3 className="text-lg font-medium mb-4">管理员工具</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>团队成员管理</CardTitle>
                  <CardDescription>管理团队成员和权限</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button onClick={() => router.push("/users")}>
                    管理成员
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>模型配置</CardTitle>
                  <CardDescription>配置和管理AI模型</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button onClick={() => router.push("/models")}>
                    配置模型
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </CustomContainer>
  )
}
