{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@radix-ui/react-separator": "1.1.6", "@radix-ui/react-slot": "^1.1.2", "@ragtop-web/ui": "workspace:*", "@tanstack/react-query": "5.76.1", "class-variance-authority": "^0.7.1", "jotai": "^2.7.0", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.51.0", "sonner": "2.0.3", "zod": "^3.24.2"}, "devDependencies": {"@ragtop-web/eslint-config": "workspace:^", "@ragtop-web/typescript-config": "workspace:*", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5.7.3"}}