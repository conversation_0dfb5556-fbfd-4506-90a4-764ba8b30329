"use client"

import { useEffect, useState } from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"

import { Button } from "./button.tsx"

/**
 * 主题切换组件
 *
 * 提供一个按钮，点击即可在亮色和暗色主题之间切换
 */
export function ThemeToggle() {
  const { setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // 在组件挂载后再获取主题，避免服务端渲染时的水合问题
  useEffect(() => {
    setMounted(true)
  }, [])

  // 切换主题函数
  const toggleTheme = () => {
    if (!mounted) return
    const currentTheme = resolvedTheme === 'dark' ? 'light' : 'dark'
    setTheme(currentTheme)
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      aria-label="切换主题"
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">切换主题</span>
    </Button>
  )
}
