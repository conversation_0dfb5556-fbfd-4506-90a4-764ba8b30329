"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useIsTeamAdmin } from "@/lib/user-role"

/**
 * 首页组件
 *
 * 根据用户角色自动重定向到相应页面
 * - 管理员：重定向到用户管理页面
 * - 普通成员：重定向到首页
 */
export default function HomePage() {
  const router = useRouter()
      const isAdmin = useIsTeamAdmin()

  useEffect(() => {
    // 检查用户是否是当前团队的管理员
    if (isAdmin) {
      // 管理员重定向到用户管理页面
      router.push("/users")
    } else {
      // 普通成员重定向到首页
      router.push("/home")
    }
  }, [router])

  return null
}
